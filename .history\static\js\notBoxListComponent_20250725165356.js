/**
 * 漏装箱列表组件
 * 用于在装箱完成后和调拨详情页面显示漏装箱记录
 */
window.NotBoxListComponent = {
    // 组件状态
    notBoxSelect: [],
    pick_admin_name: '',
    band_admin_id: '',
    source_no: '',
    allot_id: '',

    // layui 模块
    table: null,
    form: null,
    layer: null,
    $: null,
    iframeTools: null,

    /**
     * 初始化组件
     * @param {Object} layui - layui对象
     * @param {string} sourceNo - 来源单号
     * @param {string} allotId - 调拨ID（可选，用于未见实物标记）
     */
    init: function(layui, sourceNo, allotId = null) {
        this.table = layui.table;
        this.form = layui.form;
        this.layer = layui.layer;
        this.$ = layui.jquery;
        this.iframeTools = layui.iframeTools || window.iframeTools;
        this.source_no = sourceNo;
        this.allot_id = allotId || sourceNo;

        // 重置状态
        this.resetState();

        // 绑定事件
        this.bindEvents();

        // 确保HTML结构存在
        this.ensureHTMLStructure();
    },

    /**
     * 重置状态
     */
    resetState: function() {
        this.notBoxSelect = [];
        this.pick_admin_name = '';
        this.band_admin_id = '';
    },

    /**
     * 确保HTML结构存在
     */
    ensureHTMLStructure: function() {
        if (this.$('#notBoxTableDiv').length === 0) {
            const htmlStructure = `
                <div id="notBoxTableDiv" style="display: none">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <form id="searchParam" class="layui-form layui-form-pane" action="">
                                <div class="layui-row mt-7">
                                    <div class="layui-col-xs6">
                                        <label class="layui-form-label">拣货人</label>
                                        <div class="layui-input-inline" style="width: 60%">
                                            <input type="text" name="pick_admin_name" autocomplete="off" class="layui-input" id="pick_admin_name">
                                        </div>
                                    </div>
                                    <div class="layui-col-xs6">
                                        <label class="layui-form-label">是否绑定</label>
                                        <div class="layui-input-inline" style="width: 60%">
                                            <select name="band_admin_id" class="layui-select">
                                                <option value=""></option>
                                                <option value="1">是</option>
                                                <option value="0">否</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div style="display: flex;justify-content: flex-end;">
                                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
                                            <i class="layui-icon layui-icon-search"></i>
                                            查询
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="notBoxTable"></table>
                </div>
            `;
            this.$('body').append(htmlStructure);
        }

        // 确保复选框模板存在
        if (this.$('#checkbd').length === 0) {
            const checkboxTemplate = '<script type="text/html" id="checkbd">' +
                '{{# if(d.e_type != 1){ }}' +
                '<input type="checkbox" name="check_one" title="" lay-skin="primary" lay-filter="checkone" data-pick_order_detail_id="{{ d.pick_order_detail_id }}" data-pick_order_no="{{ d.pick_order_no }}" data-unique_code="{{ d.unique_code }}" data-e_type="{{ d.e_type }}">' +
                '{{# } }}' +
                '</script>';
            this.$('body').append(checkboxTemplate);
        }
    },

    /**
     * 显示漏装箱列表弹窗
     * @param {Function} onClose - 关闭回调函数
     */
    show: function(onClose) {
        const self = this;

        // 重置搜索条件
        this.resetSearchQuery();

        // 加载漏装箱列表数据
        this.getNotBoxTable();

        // 打开弹窗
        this.layer.open({
            type: 1,
            title: '漏装箱记录',
            closeBtn: 1,
            area: ['80%', '80%'],
            shade: 0.1,
            id: 'LAY_notBoxList_modal',
            resize: true,
            btn: ['未见实物', '导出', '打印', '关闭'],
            btnAlign: 'c',
            moveType: 1,
            content: this.$("#notBoxTableDiv"),
            yes: function(index, layero) {
                console.log('未见实物');
                if (self.notBoxSelect.length === 0) {
                    self.layer.msg('请选择需要标记的店内码！');
                    return false;
                }
                self.notSeenMark();
            },
            btn2: function(index, layero) {
                console.log('导出');
                self.exportNotBoxTable();
            },
            btn3: function(index, layero) {
                console.log('打印');
                if (self.notBoxSelect.length === 0) {
                    self.layer.msg('请选择需要打印的店内码！');
                    return false;
                }
                self.batchPrintNotBoxList();
            },
            btn4: function(index, layero) {
                console.log('关闭');
                self.layer.close(index);
                if (onClose && typeof onClose === 'function') {
                    onClose();
                }
            },
            cancel: function(index, layero) {
                if (onClose && typeof onClose === 'function') {
                    onClose();
                }
            }
        });
    },

    /**
     * 重置搜索条件
     */
    resetSearchQuery: function() {
        this.pick_admin_name = '';
        this.band_admin_id = '';
        this.notBoxSelect = [];
        if (this.$('#searchParam')[0]) {
            this.$('#searchParam')[0].reset();
        }
    },

    /**
     * 获取漏装箱表格数据
     */
    getNotBoxTable: function() {
        const self = this;
        this.table.render({
            elem: '#notBoxTable',
            url: '/goodsPacking/notBoxList',
            where: {
                export: 0,
                source_no: this.source_no,
                pick_admin_name: this.pick_admin_name,
                band_admin_id: this.band_admin_id,
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.total,
                    "data": res.data.data
                };
            },
            cols: [[
                {
                    templet: "#checkbd",
                    title: "<input type='checkbox' name='check_all' title='' lay-skin='primary' lay-filter='checkall' > ",
                    width: 60,
                },
                {field: 'pick_order_no', title: '拣货单号', width: 160},
                {field: 'pick_admin_name', title: '拣货人'},
                {field: 'category_names', title: '三级类目'},
                {field: 'brand_name', title: '品牌'},
                {field: 'shelf_code', title: '货位'},
                {field: 'unique_code', title: '店内码'},
                {field: 'band_admin_name', title: '绑定人'}
            ]],
            skin: 'line',
            page: true,
            limit: 10,
            done: function(res) {
                self.$("input[name='siam_all']").prop("checked", false);
                self.form.render("checkbox");
            }
        });
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        const self = this;

        // 监听每行单选
        this.form.on('checkbox(checkone)', function(d) {
            self.handleRowCheckbox(d);
        });

        // 监听全选
        this.form.on('checkbox(checkall)', function(d) {
            self.handleSelectAll(d);
        });

        // 监听查询表单提交
        this.form.on('submit(query)', function(data) {
            console.log('漏装箱-查询-data====', data.field);
            self.pick_admin_name = data.field.pick_admin_name;
            self.band_admin_id = data.field.band_admin_id;
            self.getNotBoxTable();
            return false;
        });
    },

    /**
     * 处理行复选框选择
     */
    handleRowCheckbox: function(d) {
        console.log("选中行数据：", d);
        console.log("选中状态：", d.elem.checked);

        let allData = this.$("input[name=check_one]");
        let checkedData = this.$("input[name=check_one]:checked");

        if (allData.length == checkedData.length && checkedData.length != 0) {
            this.$("input[name='check_all']").prop("checked", true);
        } else {
            this.$("input[name='check_all']").prop("checked", false);
        }

        this.form.render("checkbox");

        if (d.elem.checked) {
            // 不是未见实物（e_type=0）才添加
            if (d.elem.dataset.e_type == 0) {
                this.notBoxSelect.push({
                    "pick_order_no": d.elem.dataset.pick_order_no,
                    "unique_code": d.elem.dataset.unique_code,
                    "pick_order_detail_id": d.elem.dataset.pick_order_detail_id,
                });
            }
        } else {
            for (let i = 0; i < this.notBoxSelect.length; i++) {
                if (this.notBoxSelect[i].pick_order_no == d.elem.dataset.pick_order_no &&
                    this.notBoxSelect[i].unique_code == d.elem.dataset.unique_code &&
                    this.notBoxSelect[i].pick_order_detail_id == d.elem.dataset.pick_order_detail_id
                ) {
                    this.notBoxSelect.splice(i, 1);
                }
            }
        }

        console.log("当前选中数据：", this.notBoxSelect);
    },

    /**
     * 处理全选
     */
    handleSelectAll: function(d) {
        const self = this;
        console.log("全选状态：", d.elem.checked);

        let allData = this.$("input[name=check_all]:checked");
        if (allData.length > 0) {
            this.$("input[name='check_one']").prop("checked", true);
        } else {
            this.$("input[name='check_one']").prop("checked", false);
        }

        this.form.render("checkbox");

        if (d.elem.checked) {
            // 获取全选中的数据
            let s = [];
            this.$.each(this.$("input[name=check_one]:checked"), function(i, value) {
                if (self.$(value).attr("data-e_type") == 0) {
                    s.push({
                        "pick_order_no": self.$(value).attr("data-pick_order_no"),
                        "unique_code": self.$(value).attr("data-unique_code"),
                        "pick_order_detail_id": self.$(value).attr("data-pick_order_detail_id"),
                    });
                }
            });
            this.notBoxSelect = s;
        } else {
            // 全不选
            this.notBoxSelect = [];
        }

        console.log("当前选中数据：", this.notBoxSelect);
    },

    /**
     * 去重
     */
    unique: function(data) {
        return Array.from(new Set(data.map(item => item.unique_code))).map(code => {
            return data.find(item => item.unique_code === code);
        });
    },

    /**
     * 未见实物标记
     */
    notSeenMark: function() {
        const self = this;
        const index = this.layer.load(1, {
            shade: [0.1, '#fff']
        });

        let uniqueData = this.unique(this.notBoxSelect);

        this.$.ajax({
            url: '/goodsPacking/notSeenMark',
            type: 'post',
            data: {
                allot_id: this.allot_id,
                data: uniqueData
            },
            dataType: "json",
            success: function(res) {
                self.layer.close(index);
                console.log('未见实物标记-res====', res);
                if (res.code == 200) {
                    self.layer.msg('操作成功');
                    self.getNotBoxTable();
                    self.notBoxSelect = [];
                } else {
                    self.layer.msg(res.msg);
                }
            },
            error: function(res) {
                self.layer.close(index);
                self.layer.msg('操作失败');
            }
        });
    },

    /**
     * 导出漏箱记录
     */
    exportNotBoxTable: function() {
        const self = this;
        const index = this.layer.load(1, {
            shade: [0.1, '#fff']
        });

        this.$.ajax({
            url: '/goodsPacking/notBoxList',
            type: 'post',
            data: {
                export: 1,
                source_no: this.source_no,
            },
            dataType: "json",
            success: function(res) {
                self.layer.close(index);
                console.log('导出漏箱记录-res====', res);
                if (res.data.url) {
                    window.open(res.data.url);
                } else {
                    self.layer.msg(res.msg);
                }
            },
            error: function(res) {
                self.layer.close(index);
                self.layer.msg('操作失败');
            }
        });
    },

    /**
     * 打印漏装箱列表
     */
    batchPrintNotBoxList: function() {
        const pick_order_detail_ids = this.notBoxSelect.map(item => item.pick_order_detail_id).join(',');

        if (this.iframeTools) {
            this.iframeTools.editTab('批量打印漏箱列表',
                '/goodsPacking/batchPrintNotBoxList?export=2&source_no=' + this.source_no + '&pick_order_detail_ids=' + pick_order_detail_ids);
        } else {
            window.open('/goodsPacking/batchPrintNotBoxList?export=2&source_no=' + this.source_no + '&pick_order_detail_ids=' + pick_order_detail_ids);
        }
    }
}

// 组件已定义完成，可以通过 window.NotBoxListComponent 访问
