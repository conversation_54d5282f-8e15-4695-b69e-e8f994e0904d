/**
 * 漏装箱列表组件
 * 用于在装箱完成后和调拨详情页面显示漏装箱记录
 */
class NotBoxListComponent {
    constructor() {
        this.notBoxSelect = []; // 选中的漏装箱记录
        this.pick_admin_name = '';
        this.band_admin_id = '';
        this.source_no = '';
        this.table = null;
        this.form = null;
        this.layer = null;
        this.$ = null;
    }

    /**
     * 初始化组件
     * @param {Object} layui - layui对象
     * @param {string} sourceNo - 来源单号
     * @param {string} allotId - 调拨ID（可选，用于未见实物标记）
     */
    init(layui, sourceNo, allotId = null) {
        this.table = layui.table;
        this.form = layui.form;
        this.layer = layui.layer;
        this.$ = layui.jquery;
        this.source_no = sourceNo;
        this.allot_id = allotId || sourceNo; // 如果没有提供allotId，使用sourceNo

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 显示漏装箱列表弹窗
     * @param {Function} onClose - 关闭回调函数
     */
    show(onClose) {
        // 重置搜索条件
        this.resetSearchQuery();
        
        // 加载漏装箱列表数据
        this.getNotBoxTable();
        
        // 打开弹窗
        this.layer.open({
            type: 1,
            title: '漏装箱记录',
            closeBtn: 1,
            area: ['80%', '80%'],
            shade: 0.1,
            id: 'LAY_notBoxList_modal',
            resize: true,
            btn: ['未见实物', '导出', '打印', '关闭'],
            btnAlign: 'c',
            moveType: 1,
            content: this.$("#notBoxTableDiv"),
            yes: (index, layero) => {
                console.log('未见实物');
                if (this.notBoxSelect.length === 0) {
                    this.layer.msg('请选择需要标记的店内码！');
                    return false;
                }
                this.notSeenMark();
            },
            btn2: (index, layero) => {
                console.log('导出');
                this.exportNotBoxTable();
            },
            btn3: (index, layero) => {
                console.log('打印');
                if (this.notBoxSelect.length === 0) {
                    this.layer.msg('请选择需要打印的店内码！');
                    return false;
                }
                this.batchPrintNotBoxList();
            },
            btn4: (index, layero) => {
                console.log('关闭');
                this.layer.close(index);
                if (onClose && typeof onClose === 'function') {
                    onClose();
                }
            },
            cancel: (index, layero) => {
                if (onClose && typeof onClose === 'function') {
                    onClose();
                }
            }
        });
    }

    /**
     * 重置搜索条件
     */
    resetSearchQuery() {
        this.pick_admin_name = '';
        this.band_admin_id = '';
        this.notBoxSelect = [];
        if (this.$('#searchParam')[0]) {
            this.$('#searchParam')[0].reset();
        }
    }

    /**
     * 获取漏装箱表格数据
     */
    getNotBoxTable() {
        this.table.render({
            elem: '#notBoxTable',
            url: '/goodsPacking/notBoxList',
            where: {
                export: 0,
                source_no: this.source_no,
                pick_admin_name: this.pick_admin_name,
                band_admin_id: this.band_admin_id,
            },
            parseData: (res) => {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.total,
                    "data": res.data.data
                };
            },
            cols: [[
                {
                    templet: "#checkbd",
                    title: "<input type='checkbox' name='check_all' title='' lay-skin='primary' lay-filter='checkall' > ",
                    width: 60,
                },
                {field: 'pick_order_no', title: '拣货单号', width: 160},
                {field: 'pick_admin_name', title: '拣货人'},
                {field: 'category_names', title: '三级类目'},
                {field: 'brand_name', title: '品牌'},
                {field: 'shelf_code', title: '货位'},
                {field: 'unique_code', title: '店内码'},
                {field: 'band_admin_name', title: '绑定人'}
            ]],
            skin: 'line',
            page: true,
            limit: 10,
            done: (res) => {
                this.$("input[name='siam_all']").prop("checked", false);
                this.form.render("checkbox");
            }
        });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 监听每行单选
        this.form.on('checkbox(checkone)', (d) => {
            this.handleRowCheckbox(d);
        });

        // 监听全选
        this.form.on('checkbox(checkall)', (d) => {
            this.handleSelectAll(d);
        });

        // 监听查询表单提交
        this.form.on('submit(query)', (data) => {
            console.log('漏装箱-查询-data====', data.field);
            this.pick_admin_name = data.field.pick_admin_name;
            this.band_admin_id = data.field.band_admin_id;
            this.getNotBoxTable();
            return false;
        });
    }

    /**
     * 处理行复选框选择
     */
    handleRowCheckbox(d) {
        console.log("选中行数据：", d);
        console.log("选中状态：", d.elem.checked);
        
        let allData = this.$("input[name=check_one]");
        let checkedData = this.$("input[name=check_one]:checked");
        
        if (allData.length == checkedData.length && checkedData.length != 0) {
            this.$("input[name='check_all']").prop("checked", true);
        } else {
            this.$("input[name='check_all']").prop("checked", false);
        }
        
        this.form.render("checkbox");
        
        if (d.elem.checked) {
            // 不是未见实物（e_type=0）才添加
            if (d.elem.dataset.e_type == 0) {
                this.notBoxSelect.push({
                    "pick_order_no": d.elem.dataset.pick_order_no,
                    "unique_code": d.elem.dataset.unique_code,
                    "pick_order_detail_id": d.elem.dataset.pick_order_detail_id,
                });
            }
        } else {
            for (let i = 0; i < this.notBoxSelect.length; i++) {
                if (this.notBoxSelect[i].pick_order_no == d.elem.dataset.pick_order_no &&
                    this.notBoxSelect[i].unique_code == d.elem.dataset.unique_code &&
                    this.notBoxSelect[i].pick_order_detail_id == d.elem.dataset.pick_order_detail_id
                ) {
                    this.notBoxSelect.splice(i, 1);
                }
            }
        }
        
        console.log("当前选中数据：", this.notBoxSelect);
    }

    /**
     * 处理全选
     */
    handleSelectAll(d) {
        console.log("全选状态：", d.elem.checked);
        
        let allData = this.$("input[name=check_all]:checked");
        if (allData.length > 0) {
            this.$("input[name='check_one']").prop("checked", true);
        } else {
            this.$("input[name='check_one']").prop("checked", false);
        }
        
        this.form.render("checkbox");
        
        if (d.elem.checked) {
            // 获取全选中的数据
            let s = [];
            this.$.each(this.$("input[name=check_one]:checked"), (i, value) => {
                if (this.$(value).attr("data-e_type") == 0) {
                    s.push({
                        "pick_order_no": this.$(value).attr("data-pick_order_no"),
                        "unique_code": this.$(value).attr("data-unique_code"),
                        "pick_order_detail_id": this.$(value).attr("data-pick_order_detail_id"),
                    });
                }
            });
            this.notBoxSelect = s;
        } else {
            // 全不选
            this.notBoxSelect = [];
        }
        
        console.log("当前选中数据：", this.notBoxSelect);
    }

    /**
     * 去重
     */
    unique(data) {
        return Array.from(new Set(data.map(item => item.unique_code))).map(code => {
            return data.find(item => item.unique_code === code);
        });
    }

    /**
     * 未见实物标记
     */
    notSeenMark() {
        const index = this.layer.load(1, {
            shade: [0.1, '#fff']
        });
        
        let uniqueData = this.unique(this.notBoxSelect);

        this.$.ajax({
            url: '/goodsPacking/notSeenMark',
            type: 'post',
            data: {
                allot_id: this.source_no, // 这里可能需要根据实际情况调整
                data: uniqueData
            },
            dataType: "json",
            success: (res) => {
                this.layer.close(index);
                console.log('未见实物标记-res====', res);
                if (res.code == 200) {
                    this.layer.msg('操作成功');
                    this.getNotBoxTable();
                    this.notBoxSelect = [];
                } else {
                    this.layer.msg(res.msg);
                }
            },
            error: (res) => {
                this.layer.close(index);
                this.layer.msg('操作失败');
            }
        });
    }

    /**
     * 导出漏箱记录
     */
    exportNotBoxTable() {
        const index = this.layer.load(1, {
            shade: [0.1, '#fff']
        });
        
        this.$.ajax({
            url: '/goodsPacking/notBoxList',
            type: 'post',
            data: {
                export: 1,
                source_no: this.source_no,
            },
            dataType: "json",
            success: (res) => {
                this.layer.close(index);
                console.log('导出漏箱记录-res====', res);
                if (res.data.url) {
                    window.open(res.data.url);
                } else {
                    this.layer.msg(res.msg);
                }
            },
            error: (res) => {
                this.layer.close(index);
                this.layer.msg('操作失败');
            }
        });
    }

    /**
     * 打印漏装箱列表
     */
    batchPrintNotBoxList() {
        const pick_order_detail_ids = this.notBoxSelect.map(item => item.pick_order_detail_id).join(',');
        
        // 这里需要根据实际的iframeTools实现来调整
        if (typeof iframeTools !== 'undefined') {
            iframeTools.editTab('批量打印漏箱列表', 
                '/goodsPacking/batchPrintNotBoxList?export=2&source_no=' + this.source_no + '&pick_order_detail_ids=' + pick_order_detail_ids);
        } else {
            // 如果没有iframeTools，直接打开新窗口
            window.open('/goodsPacking/batchPrintNotBoxList?export=2&source_no=' + this.source_no + '&pick_order_detail_ids=' + pick_order_detail_ids);
        }
    }
}

// 创建全局实例
window.notBoxListComponent = new NotBoxListComponent();

// 全局函数，供页面调用
function showNotBoxListModal(sourceNo, onClose) {
    if (typeof layui !== 'undefined') {
        window.notBoxListComponent.init(layui, sourceNo);
        window.notBoxListComponent.show(onClose);
    } else {
        console.error('layui is not defined');
    }
}
