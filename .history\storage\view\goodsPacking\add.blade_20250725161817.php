<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>{{ $title }}</title>
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link rel="stylesheet" href="/static/layui/css/layui.css" />
		<link rel="stylesheet" href="/static/css/myui.css"/>
		<link href="/static/css/pearCommon.css" rel="stylesheet"/>
		<style id="pearone-bg-color">
			.comments {
				width:100%;/*自动适应父布局宽度*/
				overflow:auto;
				word-break:break-all;/*在ie中解决断行问题(防止自动变为在一行显示，主要解决ie兼容问题，ie8中当设宽度为100%时，文本域类容超过一行时，当我们双击文本内容就会自动变为一行显示，所以只能用ie的专有断行属性“word-break或word-wrap”控制其断行)*/
			}

			.layui-table-body{
				overflow-x: hidden;
			}
			.footer-div {
				z-index: 10;
				position: fixed;
				right: 0;
				bottom: 0;
				width: 100%;
				height: 92px;
				background-color: #fff;
				border-top: 1px solid #f8f8f8;
			}
			.footer-div-inner {
				padding-top: 7px;
				padding-bottom: 7px;
				border: 1px solid #e8ebec;
				width: 100%;
				height: 55px;
				background-color: #fff;
				/*box-shadow: 0 1px 6px #888888;*/
			}
		</style>
	</head>
	<body id="body" class="p-16">

		<div class="layui-tab layui-tab-brief" lay-filter="tab_gp">
			<ul class="layui-tab-title">
				<li class="layui-this">店内码</li>
				<li>RFID</li>
				<li>条码</li>
				<li id="batch_gp_tab">批量装箱</li>
			</ul>
			<div class="layui-tab-content" style="margin-bottom: 70px">
				{{--店内码--}}
				<div class="layui-tab-item layui-show">
					<div class="layui-row">
						<div class="layui-col-xs4">
							<span>装箱扫描区</span>
							<div class="layui-row mt-7">
								<div class="layui-col-xs9 mr-20" style="height: 200px">
									<textarea id="unique_input" placeholder="请扫描店内码一行一个" class="layui-textarea" style="height: 330px"></textarea>
								</div>
								<div id="uniqueCodeCheck" class="layui-col-xs2 bg_heavy_green txt-c col f_evn clr_white tap" style="height: 330px">
									<div>
										已扫描<br><span id="uniqueScannedNum">0</span>
									</div>
									<div>
										确定
									</div>
								</div>
							</div>
						</div>
						<div class="layui-col-xs8">
							<div class="row f_start">
								<span class="mr-20">扫描数量：<span class="clr_black scan_total" id="single_box_nos_unique_scan_total">0</span></span>
								<span class="mr-20">异常数量：<span class="clr_danger_red e_num" id="single_box_nos_unique_e_num">0</span></span>
								<span class="mr-20">装箱箱号：<span class="clr_theme">{{$boxNo}}</span></span>
								<span class="mr-20">来源单号：<span class="clr_theme">{{$params['source_no']}}</span></span>
							</div>
							<div class="row h-hvc row h-hvc mt-7">
								<table id="unique_check_e_result" lay-filter=""></table>
							</div>
						</div>
					</div>
					<div class="layui-row">
						<div class="layui-col-xs12 mt-16">
							<div class="row h-vc h_btwn">
								<span class="">正常数量 <span class="fs28 bolder clr_green normal_num" id="single_box_nos_unique_normal_num">0</span></span>
								<button class="layui-btn" id="exportUniqueCode" style="margin: 5px">
									<i class="bold layui-icon layui-icon-download-circle"></i>
									下载
								</button>
							</div>
							<table id="unique_check_normal_result" lay-filter=""></table>
						</div>
					</div>
				</div>
				{{--RFID--}}
				<div class="layui-tab-item">
					<div class="layui-row">
						<div class="layui-col-xs4 col v-vhc">
							<span class="mb-16">装箱扫描区</span>
							{{--环形进度条--}}
							<canvas id="mycanvas" class="tap" width="200" height="200"></canvas>
						</div>
						<div class="layui-col-xs8">
							<div class="row f_start">
								<span class="mr-20">扫描数量：<span class="clr_black" id="single_box_nos_epc_scan_total">0</span></span>
								<span class="mr-20">异常数量：<span class="clr_danger_red" id="single_box_nos_epc_e_num">0</span></span>
								<span class="mr-20">装箱箱号：<span class="clr_theme">{{$boxNo}}</span></span>
								<span class="mr-20">来源单号：<span class="clr_theme">{{$params['source_no']}}</span></span>
							</div>
							<div class="row h-hvc row h-hvc mt-7">
								<table id="epc_check_e_result" lay-filter="uploadTxt"></table>
							</div>
						</div>
					</div>
					<div class="layui-row">
						<div class="layui-col-xs12 col mt-16">
							<div class="row h-vc h_btwn">
								<span class="">正常数量 <span class="fs28 bolder clr_green" id="single_box_nos_epc_normal_num">0</span></span>
								<button id="exportEpcCode"  class="layui-btn" id="exportGpSelectDetail" style="margin: 5px">
									<i class="bold layui-icon layui-icon-download-circle"></i>
									下载
								</button>
							</div>
							<table id="epc_check_normal_result" lay-filter="uploadTxt"></table>
						</div>
					</div>
				</div>
				{{--条码--}}
				<div class="layui-tab-item">
					<div class="layui-row">
						<div class="layui-col-xs4">
							<span>装箱扫描区</span>
							<div class="layui-row mt-7">
								<div class="layui-col-xs11 mr-20">
									<textarea id="barcode_input" placeholder="请扫描条码一行一个" class="layui-textarea" style="height: 330px"></textarea>
								</div>
							</div>
						</div>
						<div class="layui-col-xs8">
							<div class="layui-row h-hvc row h-t mt-7">
								<div class="layui-col-xs10">
									<table  id="barcode_scanned_list" lay-filter="scannedBarcodeTable"></table>
								</div>
								<div id="barcodeCheck" class="layui-col-xs1 bg_heavy_green txt-c col f_evn clr_white tap" style="height: 265px;margin-top:10px;margin-left: 10px">
									<div>
										已扫描<br><span id="barcodeScannedNum">0</span>
									</div>
									<div>
										确定
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="layui-row mt-16">
						<div class="layui-col-xs12">
							<div class="row f_start mt-16">
								<span class="mr-20">扫描数量：<span class="clr_black scan_total" id="single_box_nos_barcode_scan_total">0</span></span>
								<span class="mr-20">异常数量：<span class="clr_danger_red e_num" id="single_box_nos_barcode_e_num">0</span></span>
								<span class="mr-20">装箱箱号：<span class="clr_theme">{{$boxNo}}</span></span>
								<span class="mr-20">来源单号：<span class="clr_theme">{{$params['source_no']}}</span></span>
							</div>
							<div class="row h-hvc row h-hvc mt-7">
								<table id="barcode_check_e_result" lay-filter=""></table>
							</div>
						</div>
					</div>
					<div class="layui-row">
						<div class="layui-col-xs12 mt-16">
							<div class="row h-vc h_btwn">
								<span class="">正常数量 <span class="fs28 bolder clr_green normal_num" id="single_box_nos_barcode_normal_num">0</span></span>
								<button id="exportBarcode"  class="layui-btn" id="exportGpSelectDetail" style="margin: 5px">
									<i class="bold layui-icon layui-icon-download-circle"></i>
									下载
								</button>
							</div>
							<table id="barcode_check_normal_result" lay-filter="uploadTxt"></table>
						</div>
					</div>
				</div>
				{{--批量装箱--}}
				<div class="layui-tab-item">
					<div class="layui-row">
						<div class="layui-col-xs4">
							<span>装箱扫描区</span>
							<div class="layui-row mt-7">
								<div class="layui-col-xs11 mr-20 row h-hvc" style="height: 330px;border-right: 1px solid #e6e6e6">
									<div id="uploadDiv" style="width: 60%">
										<form  class="layui-form" class="col v-vhc">
											<div class="layui-form-item">
												<button type="button" class="layui-btn layui-btn-default" id="upload"><i class="layui-icon"></i>上传文件</button>
											</div>
											<div class="layui-form-item">
												<input lay-filter="uploadForm" type="radio" name="upload_type" value="1" title="店内码" checked>
												<input lay-filter="uploadForm" type="radio" name="upload_type" value="2" title="条码" >
											</div>
											<div class="layui-form-item clr_gray">
												<span class="block">文件格式：txt</span>
												<span id="unique_tips" style="display: block" class="block">数据格式：箱号，店内码，员工ID</span>
												<span id="barcode_tips" style="display: none"  class="block">数据格式：箱号，条形码，数量，员工ID</span>
											</div>
										</form>
									</div>
								</div>
							</div>
						</div>
						<div class="layui-col-xs8">
							<div class="row f_start">
								<span class="mr-20">扫描数量：<span class="clr_black scan_total" id="bulk_box_nos_scan_total">0</span></span>
								<span class="mr-20">异常数量：<span class="clr_danger_red e_num" id="bulk_box_nos_e_num">0</span></span>
{{--								<span class="mr-20">装箱箱号：<span class="clr_theme">{{$boxNo}}</span></span>--}}
								<span class="mr-20">来源单号：<span class="clr_theme">{{$params['source_no']}}</span></span>
							</div>
							<div class="row h-hvc row h-hvc mt-7">
								<table id="bulk_unique_check_e_result" lay-filter=""></table>
							</div>
						</div>
					</div>
					<div class="layui-row">
						<div class="layui-col-xs12 mt-16">
							<div class="row h-vc h_btwn">
								<span class="">正常数量 <span class="fs28 bolder clr_green normal_num" id="bulk_box_nos_normal_num">0</span></span>
								<button id="exportBulk" class="layui-btn" id="exportGpSelectDetail" style="margin: 5px">
									<i class="bold layui-icon layui-icon-download-circle"  ></i>
									下载
								</button>
							</div>

							<div class="layui-tab" lay-filter="checkResult">
								<ul class="layui-tab-title">
									<li class="layui-this">装箱明细</li>
									<li>汇总</li>
								</ul>
								<div class="layui-tab-content">
									<div class="layui-tab-item layui-show">
										{{--装箱明细列表--}}
										<table id="bulk_unique_check_normal_result" lay-filter=""></table>
									</div>
									<div class="layui-tab-item">
										{{--汇总--}}
										<div class="layui-tab-item layui-show">
											{{--装箱明细列表--}}
											<table id="bulk_check_normal_result_summary" lay-filter=""></table>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>
			</div>
			<input type="hidden" name = 'task_type' value="{{$params['task_type']}}" />
			<input type="hidden" name = 'source_no' value="{{$params['source_no']}}" />
			<input type="hidden" name = 'boxNo' value="{{$boxNo}}" />
			<input type="hidden" name = 'leadSealNo' value="{{$leadSealNo}}" />
			<input type="hidden" name = 'pk_id' value="{{$params['pk_id'] ?? 0}}" />
			<input type="hidden" id="page_flag" name = 'page_flag' value="{{$params['flag']}}" />
		</div>
		{{--底部按钮--}}
		<div class=" footer-div v-vc " >
			<div class="footer-div-inner row f_end h-vcr">
				<button style="width: 130px" type="button" class="mlr16 layui-btn layui-btn-lg" id="submitBtn" >装箱</button>
				<button style="width: 130px" type="button" class="mlr16 layui-btn layui-btn-lg layui-btn-primary" id="back">返回</button>
			</div>
		</div>

		{{-- 漏装箱列表弹窗内容 --}}
		<div id="notBoxTableDiv" style="display: none">
			<div class="layui-card">
				<div class="layui-card-body">
					<form id="searchParam" class="layui-form layui-form-pane" action="">
						<div class="layui-row mt-7">
							<div class="layui-col-xs6">
								<label class="layui-form-label">拣货人</label>
								<div class="layui-input-inline" style="width: 60%">
									<input type="text" name="pick_admin_name" autocomplete="off" class="layui-input" id="pick_admin_name">
								</div>
							</div>
							<div class="layui-col-xs6">
								<label class="layui-form-label">是否绑定</label>
								<div class="layui-input-inline" style="width: 60%">
									<select name="band_admin_id" class="layui-select">
										<option value=""></option>
										<option value="1">是</option>
										<option value="0">否</option>
									</select>
								</div>
							</div>
						</div>
						<div class="layui-form-item">
							<div  style="display: flex;justify-content: flex-end;">
								<button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
									<i class="layui-icon layui-icon-search"></i>
									查询
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
			<table id="notBoxTable" ></table>
		</div>

		{{-- 复选框模板 --}}
		<script type="text/html" id="checkbd">
			@verbatim
			{{# if(d.e_type != 1){ }}
			<input type="checkbox" name="check_one" title="" lay-skin="primary" lay-filter='checkone' data-pick_order_detail_id="{{ d.pick_order_detail_id }}" data-pick_order_no="{{ d.pick_order_no }}" data-unique_code="{{ d.unique_code }}" data-e_type="{{ d.e_type }}">
			{{# } }}
			@endverbatim
		</script>

		<script src="/static/layui/layui.js"></script>
		<script src="/static/js/jquery.min.js"></script>
{{--		<script src="/static/js/circle-progress.js"></script>--}}
		<script type="text/html" id="scannedBarcodeOpt">
			<a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="scannedBarcodeDel">删除</a>
		</script>
        <script src="/static/js/box.js"></script>
		<script>
			// 禁止按空格键时使用页面page down页面下翻滚动事件
			// Prevent Spacebar Doing Page Down
			function PSDPD_KeyCheck(key){
				// Don't modify text editing
				if (key.target.nodeName == "INPUT" || key.target.nodeName == "TEXTAREA" || key.target.nodeName == "SELECT") return;
				if (key.target.hasAttribute("contenteditable") && key.target.getAttribute("contenteditable") == "true") return;
				// Don't modify certain combinations
				if (key.ctrlKey || key.altKey || key.metaKey) return;
				// If it's a space character, kill the event
				if (key.key == ' '){
					key.stopPropagation();
					key.preventDefault();
					return false;
				}
			}
			// Monitor the keydown event
			document.addEventListener('keydown', PSDPD_KeyCheck);

			var page_flag = document.getElementById('page_flag').value
			if ('append' === page_flag) {
				console.log('上个页面传来的参数flag1='+page_flag)
				$('#batch_gp_tab').hide()
			} else {
				console.log('上个页面传来的参数flag2='+page_flag)
			}

			layui.config({
				base: '/static/js/' //自定义模块
			}).use(['table','element','form','upload','jquery','layer','iframeTools','closeTools'], function(){
				var element = layui.element
				var $ = layui.jquery
				var layer = layui.layer
				var form = layui.form
				var table = layui.table
				var upload = layui.upload
				var iframeTools = layui.iframeTools
				var closeTools = layui.closeTools
				const BASE_URL = 'http://127.0.0.1:30010'

				// 全局变量集合
				const state = {
					isStarted: false, // 是否已开启
					isRealDisConnected: true, // 是否真实关闭了RFID服务
					curGatherWay: 3, // 默认条码 采集方式：1=RFID,2=条形码,3店内码
					curScanStatus: 'notScan',
					// curScanTotal: 0,
					scanData: [],
					goodsCodes: [],
					acuGoodsCodes: [], // 累计正常数据
					finalEpcCodes: [], // 最终要提交校验得epc
					normalData: [], // 校验后的正常epc
					exceptionData: [], // 校验异常数据
					exceptionNum: 0, // 异常epc数
					normalNum: 0 ,// 正常epc数
					NotStoredBarcodeData: {}, // 超出任务数的未出库条码
					task_type: $('input[name=task_type]').val(),
					source_no: $('input[name=source_no]').val(),
					boxNo: $('input[name=boxNo]').val(), // 箱号
					leadSealNo: $('input[name=leadSealNo]').val(),// 铅封号
					finalBarcode: [], // 处理后要展示的已扫描条码数据
					finalUniqueCode: [], // 处理后要展示的已扫描店内码数据
					upload_type: 0, // 上传类型默认店内码
					uploader: null,
					isCheckedDone: true, // 是否校验结束


					// 批量店内码
					bulk_box_nos_scan_total: 0,
					bulk_box_nos_normal_num: 0,
					bulk_box_nos_normal_data: [],
					bulk_box_nos_normal_data_summary: [],
					bulk_box_nos_e_num: 0,
					bulk_box_nos_e_data: [],
					// 单箱epc码
					single_box_nos_epc_code_scan_total: 0,
					single_box_nos_epc_code_normal_num: 0,
					single_box_nos_epc_code_e_num: 0,
					single_box_nos_epc_code_normal_data: [],
					single_box_nos_epc_code_e_data: [],
					// 单箱店内码
					single_box_nos_unique_scan_total: 0,
					single_box_nos_unique_normal_num: 0,
					single_box_nos_unique_e_num: 0,
					single_box_nos_unique_normal_data: [],
					single_box_nos_unique_e_data: [],
					// 单箱条码
					single_box_nos_barcode_scan_total: 0,
					single_box_nos_barcode_normal_num: 0,
					single_box_nos_barcode_e_num: 0,
					single_box_nos_barcode_normal_data: [],
					single_box_nos_barcode_e_data: [],

					pk_id: $('input[name=pk_id]').val(),
					page_flag: $('input[name=page_flag]').val(),

                    layerConfirmIndex: null
				};

				console.log('装箱ID：'+state.pk_id)

				// 删除校验结果
				deleteCheckedResult()

				//tab事件触发
				element.on('tab(tab_gp)', function(data){
					console.log(data.index);
					// 店内码
					if (data.index === 0) {
						state.curGatherWay = 3
						state.upload_type = 0
						renderSingleBoxUniqueData()

					}
					// RFID
					if (data.index === 1) {
						state.curGatherWay = 1
						state.upload_type = 0
						renderSingleBoxEpcData()
						// scanEpcTable('#epc_check_e_result',state.e_data)
						// scanEpcTable('#epc_check_normal_result',state.normal_data)


					}
					// 条码
					if (data.index === 2) {
						state.curGatherWay = 2
						state.upload_type = 0
						renderSingleBoxBarcodeData()
						// scanTable('#barcode_check_e_result',state.e_data)
						// scanTable('#barcode_check_normal_result',state.normal_data)

						// 渲染已扫条码列表
						//scannedBarcodeList('#barcode_scanned_list',state.finalBarcode)


					}
					// 批量装箱
					if (data.index === 3) {
						state.curGatherWay = 23 // 条码店内码批量上传
						let upload_type = $("input[name='upload_type']:checked").val();
						state.upload_type = upload_type
						renderBulkData()
					}
				});
				renderSingleBoxUniqueData()

				scannedBarcodeList('#barcode_scanned_list',state.finalBarcode)
				function scannedBarcodeList(position,data = []) {
					table.render({
						elem: position
						// , url: '/goodsPacking/getUploadData'
						// , method: "post"
						// , where: {
						// 	//st_way:st_way
						// }
						, limit: 5
						, data: data
						, cols: [[
							{field: 'barcode', title: '条码'}
							,{field: 'num', title: '数量', edit: 'text'}
								// , {field: 'num', title: '数量', templet: function (d) {
							// 		return `<input type="text" style="width: 70px" class="layui-input" name="num" value="${d.num}" lay-filter="numInput">`
							// }}
							,{fixed: 'right', title: '操作',align: 'center', toolbar: '#scannedBarcodeOpt'}
						]]
						, page: true
					});
				}
				table.on("row(scannedBarcodeTable)", function (obj) {
					console.log(obj.data) //得到当前行数据
					//obj.del(); //删除当前行
					obj.update('num',2) //修改当前行数据
				})
				table.on("edit(scannedBarcodeTable)", function (obj) {
					// 编辑数据回显
					const data = obj.data

					// 查找元素
					let index = state.finalBarcode.findIndex((item) => item.barcode === data.barcode)
					let oldNum = $(this).prev().text()
					let newNum = data.num
					if (isNaN(Number(newNum,10)) || !/(^[0-9]\d*$)/.test(newNum)) {
						layer.msg('修改数量必须是正整数',{icon: 5})
						renderAndSetNumAgain(index,oldNum)
						return false
					}
					if (newNum < 1) {
						layer.msg('修改数量不能小于1',{icon: 5})
						renderAndSetNumAgain(index,oldNum)
						return false
					} else {
						state.finalBarcode[index].num = newNum
						state.finalBarcode[index].has_modified = 1
					}
					// 重新设置确定按钮的值
					getAndSetBarcodeScannedNum()
				})

				function renderAndSetNumAgain(index,oldNum) {
					state.finalBarcode[index].num = oldNum
					scannedBarcodeList('#barcode_scanned_list',state.finalBarcode)
					// 重新设置确定按钮的值
					getAndSetBarcodeScannedNum()
				}

				function getAndSetBarcodeScannedNum() {
					// 获取当前已扫描数量
					let num = 0
					state.finalBarcode.map(item => {
						num = parseInt(num) + parseInt(item.num)
					})
					$('#barcodeScannedNum').text(num)
				}

				function getAndSetUniqueScannedNum() {
					// 获取当前已扫描数量
					$('#uniqueScannedNum').text(state.finalUniqueCode.length)
				}

				table.on("tool(scannedBarcodeTable)", function (obj) {
					// 编辑数据回显
					const event = obj.event
					const data = obj.data
					console.log("event==",event)
					console.log("data==",data)
					if (event === 'scannedBarcodeDel') {
						// 删除条码
						delItem(state.finalBarcode, {key:'barcode',value: data.barcode})
						// 重新设置确定按钮的值
						getAndSetBarcodeScannedNum()
					}
				})

				// 删除数组元素
				function delItem(arr,obj) {
					console.log('arr===============',arr)
					console.log('obj===============',obj)
					// 找到key
					let index = arr.findIndex((item) => item[obj.key] === obj.value)
					console.log('index===============',index)
					// 删除
					state.finalBarcode.splice(index,1)

					// 删除后重新渲染
					scannedBarcodeList('#barcode_scanned_list',state.finalBarcode)
				}

				function scanEpcTable(position,data = []) {
					table.render({
						elem: position
						// , url: '/goodsPacking/getUploadData'
						// , method: "post"
						// , where: {
						// 	//st_way:st_way
						// }
						, limit: 5
						, data: data
						, cols: [[
							{type: 'numbers', title: '序号'}
							, {field: 'box_no', title: '箱号'}
							, {field: 'epc_code', title: 'EPC码'}
							, {field: 'goods_code_type', title: '类型',templet: function (d) {
									// 1=RFID 2=条形码 3=店内码
									if (d.unique_code) {
										return '店内码'
									}
									if (d.barcode) {
										return '条形码'
									}
									return ''
								}}
							, {field: 'goods_code', title: '店内码/条码',templet: function (d) {
									if (d.unique_code) {
										return d.unique_code
									}
									if (d.barcode) {
										return d.barcode
									}
									return ''
								}}
							, {field: 'num', title: '数量'}
							, {field: 'exception', title: '结果',templet: function (d) {
									let str = ''
									for (let i = 0; i < d.exception.length; i++) {
										if (d.e_type === 2) {
											str += `<span style="color: green">${d.exception[i]}</span><br>`
										} else {
											str += `<span style="color: red">${d.exception[i]}</span><br>`
										}
									}
									return str
								}}

						]]
						, page: true
					});
				}

				function scanTable(position,data = []) {
					table.render({
						elem: position
						, limit: 5
						, data: data
						, cols: [[
							{type: 'numbers', title: '序号'}
							, {field: 'box_no', title: '箱号'}
							, {field: 'code_type', title: '类型',templet: function (d) {
									// 1=RFID 2=条形码 3=店内码
									if (typeof d.unique_code !== 'undefined') {
										return '店内码'
									}
									if (typeof d.barcode !== 'undefined') {
										return '条形码'
									}
								}}
							, {field: 'goods_code', title: '店内码/条码',templet: function (d) {
									if (typeof d.unique_code !== 'undefined') {
										return d.unique_code
									}
									if (typeof d.barcode !== 'undefined') {
										return d.barcode
									}
								}}
							, {field: 'num', title: '数量',templet: function (d) {
									if (typeof d.unique_code !== 'undefined') {
										return 1
									}
									if (typeof d.barcode !== 'undefined') {
										return d.num
									}
								}}
							, {field: 'exception', title: '结果',templet: function (d) {
									let str = ''
									for (let i = 0; i < d.exception.length; i++) {
										if (d.e_type === 2) {
											str += `<span style="color: green">${d.exception[i]}</span><br>`
										} else {
											str += `<span style="color: red">${d.exception[i]}</span><br>`
										}
									}
									return str
								}}
						]]
						, page: true
					});
				}

				function summaryTable(position,data=[]) {
					table.render({
						elem: position
						, limit: 5
						, data: data
						, cols: [[
								{type: 'numbers', title: '序号'}
								, {field: 'box_no', title: '箱号'}
								, {field: 'code_type', title: '类型',templet: function (d) {
										// 1=RFID 2=条形码 3=店内码
										if (1 == d.goods_code_type) {
											return '店内码'
										}
										if (2 == d.goods_code_type) {
											return '条形码'
										}
									}}
								, {field: 'total', title: '货品数'}

							]]
						, page: true
					});
					// table.render({
					// 	type: 'post',
					// 	url: "/goodsPacking/getCheckResultSummary",
					// 	elem: position
					// 	, limit: 5
					// 	, where: {
					// 		gather_way: function () {
					// 			return state.curGatherWay
					// 		},
					// 		task_type: function () {
					// 			return state.task_type
					// 		},
					// 		upload_type: function () {
					// 			return state.upload_type
					// 		},
					// 		source_no: function () {
					// 			return state.source_no
					// 		},
					// 	}
					// 	, cols: [[
					// 		{type: 'numbers', title: '序号'}
					// 		, {field: 'box_no', title: '箱号'}
					// 		, {field: 'code_type', title: '类型',templet: function (d) {
					// 				// 1=RFID 2=条形码 3=店内码
					// 				if (1 == d.goods_code_type) {
					// 					return '店内码'
					// 				}
					// 				if (2 == d.goods_code_type) {
					// 					return '条形码'
					// 				}
					// 			}}
					// 		, {field: 'total', title: '货品数'}
					//
					// 	]]
					// 	, page: true
					// });
				}

				// 扫描三种状态：notScan,scanning,suspended
				$(document).keyup(function(event){
					if (event.keyCode === 32) {// 32 Space（空格键）
						setTimeout(function () {
							switchScanStatus()
						},800)
					}
				});

				// 根据状态切换扫描进度
				function switchScanStatus() {
					if (!isCheckedDone()) {
						layer.msg('请求过于频繁')
						return false
					}
					// 切换状态
					state.isStarted = !state.isStarted

					if (state.curGatherWay === 1 && state.curScanStatus === 'notScan') { // 当前采集方式是RFID才触发，初始状态下，第一次点击开始扫描
						// 初始状态下，点击空格，先将状态改为 scanning
						turnRfidOn()
						console.log('state.curScanStatus=1===',state.curScanStatus)
						whenNotScan()
						return false
					}
					if (state.curGatherWay === 1 && state.curScanStatus === 'scanning') { // 当前采集方式是RFID才触发，scanning状态下，点击空格，则暂停扫描
						// scanning状态下，点击空格，先将状态改为下一个状态 suspended
						turnRfidOff()
						console.log('state.curScanStatus=2===',state.curScanStatus)
						// initGoodsCodes()
						whenScanning()
						return false
					}
					if (state.curGatherWay === 1 && state.curScanStatus === 'suspended') { // 当前采集方式是RFID才触发，suspended状态下，点击空格，则继续开始扫描，同时请求接口校验数据，并将上次扫描数从零开始
						// suspended状态下，点击空格，先将状态改回 scanning状态
						turnRfidOn()
						console.log('state.curScanStatus=3===',state.curScanStatus)
						whenSuspended()
						return false
					}
				}

				function turnRfidOn() {
					state.curScanStatus = 'scanning'
					state.isStarted = true
				}
				function turnRfidOff() {
					state.curScanStatus = 'suspended'
					state.isStarted = false
				}

				function initGoodsCodes() {
					state.goodsCodes = []
					state.finalEpcCodes = []

					// 圆环数置零
					progressAnimate(state.finalEpcCodes.length,'已暂停')
				}

				$('#mycanvas').on('click',function () {
					setTimeout(function () {
						switchScanStatus()
					},800)
				})

				function whenSuspended() {

					// 根据isStarted控制RFID设备
					controlRFID()

					progressAnimate(state.finalEpcCodes.length,'正在扫描...')

				}
				function whenScanning() {
					console.log('length=2===',state.finalEpcCodes.length)
					// progressAnimate(state.finalEpcCodes.length,'开始扫描')
					setProgressText('已暂停')
					// 根据isStarted控制RFID设备
					controlRFID()

					// 校验epc
					checkEpc()

				}
				function whenNotScan() {
					progressAnimate(state.finalEpcCodes.length,'正在扫描...')
					// 根据isStarted控制RFID设备
					controlRFID()
				}

				// 校验EPC
				function checkEpc() {
					if (state.finalEpcCodes.length === 0) {
						// layer.msg('货品不能为空~')
						return false
					}
					//loading层
					const index = layer.load(1, {
						shade: [0.1, '#fff'] //0.1透明度的白色背景
					});
					state.isCheckedDone = false
					$.ajax({
						type: 'post',
						url: "/goodsPacking/checkEpcCode",
						data: {
							pk_id: state.pk_id,
							gather_way: function () {
								return state.curGatherWay
							},
							task_type: state.task_type,
							source_no: state.source_no,
							box_no: state.boxNo,
							lead_seal_no: state.leadSealNo,
							goods_codes: JSON.stringify(state.finalEpcCodes),
							upload_type: function () {
								return state.upload_type
							},
						},
						success: function (res) {

							console.log('返回结果：',res)
							state.isCheckedDone = true
							layer.close(index)
							if (res.code === 200 || res.code === 600) {
								console.log('res.code==',res.code)
								console.log('res.data==',res.data)
								getAndSaveCheckResultToGlobal(res.data,'single_box_epc_code')
								renderSingleBoxEpcData()

								// 清空已扫描数据
								initGoodsCodes()

								return false
							}
							else {
								console.log('异常===',res)
								layer.alert(res.msg,function (index) {
									// 清空已扫描数据
									initGoodsCodes()
									layer.close(index)
								})
								return false
							}
						}
					})
					return false
				}

				function isCheckedDone() {
					return state.isCheckedDone
				}
				// rfid提交
				$("#submitBtn").click(function () {
                    gpSubmit()
					return false
				})

                // 创建一个全局变量来跟踪是否已经有layer.confirm弹框打开
                var layerConfirmOpen = false;

                // 创建一个函数来打开layer.confirm弹框
                function openLayerConfirm() {
                    layerConfirmOpen = true;
                    layer.confirm('确定提交吗？', {
                        btn: ['确定', '取消'],
                        cancel: function(index, layero){
                            console.log("点击了关闭x");
                            state.layerConfirmIndex = index
                            layerConfirmOpen = false
                        }
                    }, function () {
                        console.log('点击了确定按钮');
                        // 在这里执行确认按钮的逻辑
                        layerConfirmOpen = false
                        rfidSubmit()
                    }, function () {
                        console.log('点击了取消按钮');
                        layerConfirmOpen = false
                    },
                    );
                }

                // 创建一个函数来检查是否已经有layer.confirm弹框打开
                function checkLayerConfirm() {
                    if (!layerConfirmOpen) {
                        openLayerConfirm();
                    } else {
                        // 在这里执行回车键的逻辑
                        console.log('已经有确认框打开，执行回车键的逻辑');
                        layerConfirmOpen = false
                        rfidSubmit()
                    }
                }

                // 绑定回车键事件到checkLayerConfirm函数
                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter') {
                        checkLayerConfirm();
                    }
                });
                layer.close(state.layerConfirmIndex, function() { // 监听弹框关闭事件
                    console.log('弹框已关闭'); // 在这里可以执行一些逻辑，比如清空 layerConfirmOpen 变量等操作
                    layerConfirmOpen = false
                });

                function rfidSubmit() {
                    if (state.curGatherWay === 1) {
                        console.log("rfid回车提交...")
                        // 关闭RFID开关
                        if (state.isStarted) {
                            closeRFID()
                            layer.closeAll()
                            layerConfirmOpen = false
                            return false
                        } else {
                            submit()
                        }
                        return false
                    }
                }

                function closeRFID() {
                    console.log("如果开关未关闭，先关闭epc开关...")
                    turnRfidOff()
                    // 根据isStarted控制RFID设备
                    controlRFID()
                    // 校验epc
                    checkEpc()
                    // 清空已扫描数据
                    initGoodsCodes()
                    progressAnimate(state.finalEpcCodes.length,'已暂停')
                    console.log('state.finalEpcCodes=111===',state.finalEpcCodes)
                }

                function gpSubmit() {
                    // RFID
                    if (state.curGatherWay === 1) {
                        console.log("rfid提交...")
                        // 关闭RFID开关
                        // if (state.isStarted) {
                        //     closeRFID()
                        // } else {
                        //     layer.confirm('确定提交吗？', function (index) {
                        //         submit()
                        //     })
                        // }
                        checkLayerConfirm();
                        return false
                    }

                    // 条码
                    if (state.curGatherWay === 2) {
                        console.log("条码提交...")
                        layer.confirm('确定提交吗？', function (index) {
                            submit()
                        });
                        return false
                    }

                    // 店内码
                    if (state.curGatherWay === 3) {
                        console.log("店内码提交...")
                        layer.confirm('确定提交吗？', function (index) {
                            submit()
                        });
                        return false
                    }

                    // 批量上传条码或店内码
                    if (state.curGatherWay === 23) {
                        console.log("批量上传条码或店内码提交...")
                        console.log("state.upload_type...",state.upload_type)
                        layer.confirm('确定提交吗？', function (index) {
                            console.log('批量装箱==1===')
                            submit(state.upload_type)
                            // // 条码店内码批量上传
                            // if (state.upload_type === 1) {
                            // 	console.log('批量装箱-店内码提交=====')
                            // 	submit(1)
                            // 	return false
                            // }
                            //
                            // if (state.upload_type === 2) {
                            // 	console.log('批量装箱-条码提交=====')
                            // 	submit(2)
                            // 	return false
                            // }
                        });
                        // // 条码店内码批量上传
                        // if (state.upload_type === 1) {
                        // 	console.log('批量装箱-店内码提交=====')
                        // 	layer.confirm('确认提交吗？', function (index) {
                        // 		submit(1)
                        // 	});
                        // 	return false
                        // }
                        // if (state.upload_type === 2) {
                        // 	console.log('批量装箱-条码提交=====')
                        // 	layer.confirm('确认提交吗？', function (index) {
                        // 		submit(2)
                        // 	});
                        // 	return false
                        // }
                    }
                }

				// 监听返回上一级按钮点击事件
				$('#back').click(function () {
					// 关闭当前页
					console.log('点击返回...')
					realStopAndDisconnect()
					closeTools.layer_iframe_close(0, true);
					deleteCheckedResult()
				})
				// 删除校验结果
				function deleteCheckedResult() {
					$.ajax({
						type: 'post',
						url: "/goodsPacking/deleteCheckedData",
						data: {
							gather_way: function () {
								return state.curGatherWay
							},
							task_type: state.task_type,
							upload_type: function () {
								return state.upload_type
							},
							source_no: state.source_no
						},
						success: function (res) {
							console.log('删除校验结果：',res)

						}
					})
				}
				// 获取窗口索引
				var index = parent.layer.getFrameIndex(window.name)
				// 监听父层关闭，success，成功是1，失败是2
				var success = '{sh:$success}';
				console.log('getFrameIndex========================',index)
				console.log('success========================',success)
				// 返回并刷新父页面
				function backAndReloadParent() {
					// 获取窗口索引
					var index = parent.layer.getFrameIndex(window.name)
					parent.layer.close(index)
					//重新加载父页面
					parent.location.reload();
				}

				// 监听店内码扫描输入
				$("#unique_input").keydown(function(e){
					listenKeydown(e,'unique_code',"#unique_input")
					getAndSetUniqueScannedNum()
					// clearUniqueInput()
				})

				// 监听店内码扫描输入
				$("#unique_input").blur(function(e){
					console.log('unique_input失去焦点')
					let textArea = $("#unique_input").val()
					let arr = breakLineToArr(textArea)
					console.log('arr=',arr)
					// 将扫描框数据写入全局条码数组
					if (arr.length !== 0) {
						// 先清空
						// state.finalBarcode = []
						// state.goodsCodes = []
						state.finalUniqueCode = []
						arr.map(code => {
							if (code.length !== 0) {
								pushUniqueToList(code)
							}
						})
						getAndSetUniqueScannedNum()
						// clearUniqueInput()
					}
				})


				// 监听条码扫描输入
				$("#barcode_input").blur(function(e){
					console.log('barcode_input失去焦点')
					let textAreaBarcodes = $("#barcode_input").val()
					console.log('textAreaBarcodes===============',textAreaBarcodes)
					let barcodeArr = breakLineToArr(textAreaBarcodes)
					console.log('barcodeArr===============',barcodeArr)
					// 将扫描框数据写入全局条码数组
					if (barcodeArr.length !== 0) {
						// 先清空
						// state.finalBarcode = []
						// state.goodsCodes = []
						barcodeArr.map(code => {
							if (code.length !== 0) {
								pushBarcodeToList(code)
							}
						})
						clearBarcodeInput()
					}
				})

				function clearBarcodeInput() {
					$("#barcode_input").val('')
					let nreStr =  $("#barcode_input").val().replace(/\n|\r\n/g,'').replace(/\s/g,'');
					$("#barcode_input").val(nreStr)
				}

				function clearUniqueInput() {
					$("#unique_input").val('')
				}

				// 将数组内容以换行形式显示在textarea
				function arrToBreakLine(arr) {
					return arr.join('\r\n')
				}
				// 将换行形式字符串转换成数组显示在textarea
				function breakLineToArr(str) {
					str = str.trim(/[(\r\n)\r\n]+/)
					return str.split(/[(\r\n)\r\n]+/)
				}

				// 监听条码扫描输入
				$("#barcode_input").keydown(function(e){
					listenKeydown(e,'barcode',"#barcode_input")
					getAndSetBarcodeScannedNum()
					// 清空扫码框
					//clearBarcodeInput()
				})
				// $("#barcode_input").on("input",function(e){
				// 	//获取input输入的值
				// 	// console.log('监听textarea输入-this.code-',this.code)
				// 	// console.log('监听textarea输入-e-',e)
				// 	// console.log('监听textarea输入',e.originalEvent.data)
				// });

				// 扫码枪扫描到的条形码每一位会触发一次onkeydown事件
				// 比如扫描条码位‘1234567890'的条形码，会连续执行10次onkeydown事件
				// 条码扫描到最后一位，会直接触发Enter
				function listenKeydown(e,tpe,position) {
					// 键入Enter
					if(e.which === 13) {
						console.log('扫描完成')
						// 获取输入框内容
						let str = $(position).val()
						console.log('扫描后输入框内容',str)
						let scannedArr = breakLineToArr(str)
						if (tpe === 'unique_code') {
							// 将扫描框数据写入全局条码数组
							if (scannedArr.length !== 0) {
								// 先清空
								// state.finalBarcode = []
								// state.goodsCodes = []
								scannedArr.map(code => {
									if (code.length !== 0) {
										pushUniqueToList(code)
									}
								})
								// clearUniqueInput()
							}
						}
						if (tpe === 'barcode') {
							// 将扫描框数据写入全局条码数组
							if (scannedArr.length !== 0) {
								// 先清空
								// state.finalBarcode = []
								// state.goodsCodes = []
								scannedArr.map(code => {
									if (code.length !== 0) {
										pushBarcodeToList(code)
									}
								})
								clearBarcodeInput()
							}
						}
					}
				}
				function listenKeydown_bak(e,tpe) {
					let nextCode,nextTime = '';
					let code = this.code;
					if (window.event) {// IE
						nextCode = e.keyCode
					} else if (e.which) {// Netscape/Firefox/Opera
						nextCode = e.which
					}

					//字母上方 数字键0-9 对应键码值 48-57; 数字键盘 数字键0-9 对应键码值 96-105
					if((nextCode >= 48 && nextCode <= 57) || (nextCode>=96 && nextCode <= 105)){
						let codes = {'48':48,'49':49,'50':50,'51':51,'52':52,'53':53,'54':54,'55':55,'56':56,'57':57,
							'96':48,'97':49,'98':50,'99':51,'100':52,'101':53,'102':54,'103':55,'104':56,'105':57
						};
						nextCode = codes[nextCode];
					}

					if (typeof code == 'undefined') code = ''
					// 第二次输入延迟两秒，删除之前的数据重新计算
					code += String.fromCharCode(nextCode)

					// 保存数据
					this.nextCode = nextCode;
					this.code = code;

					// 键入Enter
					if(e.which === 13) {
						// 判断 code 长度（这里就获取到条码值了，以下业务自由发挥）
						code = $.trim(code)
						console.log('code：' + code)
						console.log('code-length：' + code.length)
						console.log('code.indexOf-1',code.indexOf("\\n"))
						console.log('code.indexOf-2',code.indexOf("\\n"))
						console.log('code.indexOf-3',code.indexOf('')) // -1 不存在
						if (code.indexOf('') >= 0
								|| code.indexOf('') >= 0
								|| code.indexOf('') >= 0
								|| code.indexOf('V') >= 0
								|| code.indexOf('V') >= 0
								|| code.indexOf('VAVAV') >= 0
								|| code.length === 0) {

						} else {
							// 把扫描的条码push进数组
							pushCodeToList(code,tpe)
						}
						// 键入回车务必清空code值
						this.code = ''
						return false;
					}

				}

				function pushCodeToList(code,tpe) {
					if (tpe === 'unique_code') {
						console.log('unique_code-type=======',tpe)
						console.log('unique_code-code=======',code)
						// 已存在的店内码不在写入
						pushUniqueToList(code)
						// let index = state.finalUniqueCode.findIndex(item => item === code)
						// console.log('unique_code-index=======',index)
						// if (index === -1) {
						// 	state.finalUniqueCode.push({unique_code: code,num:1})
						// 	$('#uniqueScannedNum').text(state.finalUniqueCode.length)
						// }
					}
					if (tpe === 'barcode') {
						console.log('barcode-type=======',tpe)
						console.log('barcode-code=======',code)
						pushBarcodeToList(code)
					}
				}

				function pushUniqueToList(code) {
					// 查找是否已存在
					let index = state.finalUniqueCode.findIndex((item) => item.unique_code === code)
					if (index !== -1) {

					} else {
						// 未找到，push，数组前面添加元素
						state.finalUniqueCode.unshift({unique_code:code,num:1})
					}
					// 重新设置确定按钮的值
					getAndSetBarcodeScannedNum()
				}

				function pushBarcodeToList(code) {
					// 查找是否已存在
					let index = state.finalBarcode.findIndex((item) => item.barcode === code)
					if (index !== -1) {
						// 找到，累加1
						state.finalBarcode[index].num = state.finalBarcode[index].num + 1
					} else {
						// 未找到，push，数组前面添加元素
						state.finalBarcode.unshift({barcode:code,num:1})
					}
					// 重新设置确定按钮的值
					getAndSetBarcodeScannedNum()
					// 重新渲染列表
					scannedBarcodeList('#barcode_scanned_list',state.finalBarcode)
				}

				// function convertFinalArr(obj) {
				// 	let result = []
				// 	Object.keys(obj).forEach((key) => {
				// 		console.log("key=",key)
				// 		console.log('value=',obj[key])
				// 		result.unshift({
				// 			'barcode': key,
				// 			'num': obj[key],
				// 		})
				// 	})
				// 	return result
				// }

				//获取数组某一列成组成新数组
				function array_column(arr,field) {
					arr = Array.from(arr); // 将arr用Array.from()方法转换为数组
					var arrNew = arr.map(item => {
						return item[field]
					})
					return arrNew
				}

				// 统计数组中元素出现个数
				function array_count_values(arr) {
					return arr.reduce((total,currentVal) => {
						if(currentVal in total)
						{
							total[currentVal]++;
						}
						else
						{
							total[currentVal] = 1;
						}
						return total;
					},{})
				}

				// 店内码校验
				$('#uniqueCodeCheck').on('click',function () {
					console.log('uniqueCodeCheck。。。')
					uniqueCodeCheck()
				})

				function uniqueCodeCheck() {
					$.ajax({
						type: 'post',
						url: "/goodsPacking/bulkCheckUniqueCodes",
						data: {
							pk_id: state.pk_id,
							gather_way: function () {
								return state.curGatherWay
							},
							task_type: state.task_type,
							source_no: state.source_no,
							box_no: state.boxNo,
							lead_seal_no: state.leadSealNo,
							goods_codes: JSON.stringify(state.finalUniqueCode),
							upload_type: function () {
								return state.upload_type
							},
						},
						success: function (res) {
							console.log('返回结果111：',res)
							if (200 == res.code) {
								getAndSaveCheckResultToGlobal(res.data,'single_box_unique_code')
								renderSingleBoxUniqueData()
								// 数据清空
								clearUniqueInput()
								state.finalUniqueCode = []
								getAndSetUniqueScannedNum()
							} else {
								layer.alert(res.msg)
								return false
							}
						}
					})
				}

				// function getBulkCheckResult() {
				// 	let result = {
				// 		normal_num: 0,
				// 		normal_data: [],
				// 		e_num: 0,
				// 		e_data: [],
				// 		total: 0,
				// 	}
				//
				// 	$.ajax({
				// 		type: 'post',
				// 		url: "/goodsPacking/getBulkCheckResult",
				// 		data: {
				// 			task_type: state.task_type,
				// 			gather_way: function () {
				// 				return state.curGatherWay
				// 			},
				// 			upload_type: function () {
				// 				return state.upload_type
				// 			},
				// 			source_no: state.source_no
				// 		},
				// 		async: false, // //使用同步的方式,true为异步方式
				// 		success: function (res) {
				// 			console.log('返回结果111：',res)
				// 			if (200 == res.code) {
				// 				result.normal_num = res.data.normal_num
				// 				result.e_num = res.data.e_num
				// 				result.total = res.data.normal_num+res.data.e_num
				// 				result.e_data = res.data.e_data
				// 				result.normal_data = res.data.normal_data
				// 			} else {
				// 				layer.alert(res.msg)
				// 				return false
				// 			}
				// 		}
				// 	})
				//
				// 	return result
				// }

				function renderBulkData() {

					$('#bulk_box_nos_normal_num').text(state.bulk_box_nos_normal_num)
					$('#bulk_box_nos_e_num').text(state.bulk_box_nos_e_num)
					$('#bulk_box_nos_scan_total').text(state.bulk_box_nos_scan_total)

					scanTable('#bulk_unique_check_e_result',state.bulk_box_nos_e_data,'exception')
					scanTable('#bulk_unique_check_normal_result',state.bulk_box_nos_normal_data,'normal')
				}

				// 单箱校验epc码列表渲染-正常+异常
				function renderSingleBoxEpcData() {

					$('#single_box_nos_epc_scan_total').html(state.single_box_nos_epc_code_scan_total)
					$('#single_box_nos_epc_e_num').html(state.single_box_nos_epc_code_e_num)
					$('#single_box_nos_epc_normal_num').html(state.single_box_nos_epc_code_normal_num)
					scanEpcTable('#epc_check_e_result',state.single_box_nos_epc_code_e_data)
					scanEpcTable('#epc_check_normal_result',state.single_box_nos_epc_code_normal_data)

					// state.finalEpcCodes = res.data.normal_data
				}

				// 单箱校验店内码列表渲染-正常+异常
				function renderSingleBoxUniqueData() {

					$('#single_box_nos_unique_normal_num').text(state.single_box_nos_unique_normal_num)
					$('#single_box_nos_unique_e_num').text(state.single_box_nos_unique_e_num)
					$('#single_box_nos_unique_scan_total').text(state.single_box_nos_unique_scan_total)

					scanTable('#unique_check_e_result',state.single_box_nos_unique_e_data,'exception')
					scanTable('#unique_check_normal_result',state.single_box_nos_unique_normal_data,'normal')
				}

				function renderSingleBoxBarcodeData() {

					$('#single_box_nos_barcode_normal_num').text(state.single_box_nos_barcode_normal_num)
					$('#single_box_nos_barcode_e_num').text(state.single_box_nos_barcode_e_num)
					$('#single_box_nos_barcode_scan_total').text(state.single_box_nos_barcode_scan_total)

					scanTable('#barcode_check_e_result',state.single_box_nos_barcode_e_data,'exception')
					scanTable('#barcode_check_normal_result',state.single_box_nos_barcode_normal_data,'normal')
				}

				function getAndSaveCheckResultToGlobal(result,type) {
					// let result = getBulkCheckResult()
					console.log('result================',result)
					if ('single_box_unique_code' === type) {
						state.single_box_nos_unique_normal_num = result?.normal_num
						state.single_box_nos_unique_e_num = result?.e_num
						state.single_box_nos_unique_scan_total = result?.total
						state.single_box_nos_unique_e_data = result?.e_data
						state.single_box_nos_unique_normal_data = result?.normal_data
					}
					if ('single_box_barcode' === type) {
						state.single_box_nos_barcode_normal_num = result?.normal_num
						state.single_box_nos_barcode_e_num = result?.e_num
						state.single_box_nos_barcode_scan_total = result?.total
						state.single_box_nos_barcode_e_data = result?.e_data
						state.single_box_nos_barcode_normal_data = result?.normal_data
					}
					if ('bulk_box' === type) {
						state.bulk_box_nos_normal_num = result?.normal_num
						state.bulk_box_nos_e_num = result?.e_num
						state.bulk_box_nos_scan_total = result?.total
						state.bulk_box_nos_e_data = result?.e_data
						state.bulk_box_nos_normal_data = result?.normal_data
					}
					if ('single_box_epc_code' === type) {
						state.single_box_nos_epc_code_normal_data = result?.normal_data
						state.single_box_nos_epc_code_normal_num = result?.normal_num
						state.single_box_nos_epc_code_e_num = result?.e_num
						state.single_box_nos_epc_code_e_data = result?.e_data
						state.single_box_nos_epc_code_scan_total = result?.total
					}

				}

				// 条码校验
				$('#barcodeCheck').on('click',function () {
					console.log('barcodeCheck。。。')
					barcodeCheck()
					// 校验完成后，清空已扫描数据表格
					clearBarcodeScanTable()
				})

				function clearBarcodeScanTable() {
					state.finalBarcode = []
					scannedBarcodeList('#barcode_scanned_list',state.finalBarcode)
					getAndSetBarcodeScannedNum()
				}

				function barcodeCheck() {
					console.log('state.finalBarcode===========',state.finalBarcode)
					$.ajax({
						type: 'post',
						url: "/goodsPacking/bulkCheckBarcode",
						data: {
							pk_id: state.pk_id,
							gather_way: function () {
								return state.curGatherWay
							},
							task_type: state.task_type,
							source_no: state.source_no,
							box_no: state.boxNo,
							lead_seal_no: state.leadSealNo,
							goods_codes: state.finalBarcode,
							upload_type: function () {
								return state.upload_type
							},
						},
						success: function (res) {
							console.log('返回结果111：',res)
							if (200 == res.code) {
								// state.single_box_nos_barcode_normal_num = res.data.normal_num
								// state.single_box_nos_barcode_e_num = res.data.e_num
								// state.single_box_nos_barcode_scan_total = res.data.normal_num+res.data.e_num
								// state.single_box_nos_barcode_e_data = res.data.e_data
								// state.single_box_nos_barcode_normal_data = res.data.normal_data
								getAndSaveCheckResultToGlobal(res.data,'single_box_barcode')
								renderSingleBoxBarcodeData()
							} else {
								layer.alert(res.msg)
								return false
							}
						}
					})
				}

				// uploadForm
				form.on('radio(uploadForm)', function(data){
					console.log('uploadForm=',data.value); //被点击的radio的value值
					// 店内码
					if (data.value == 1) {
						console.log('uploadForm111=',data.value);
						$('#unique_tips').show()
						$('#barcode_tips').hide()
						state.upload_type = 1
						// state.uploader.render()
					}

					// 条码
					if (data.value == 2) {
						console.log('uploadForm222=',data.value);
						$('#unique_tips').hide()
						$('#barcode_tips').show()
						state.upload_type = 2
						// state.uploader.render()
					}

					console.log('state.upload_type=',state.upload_type);
				});

				//指定允许上传的文件类型
				state.uploader = upload.render({
					elem: '#upload'
					,url: '/goodsPacking/uploadData'
					,accept: 'file' //普通文件
					,data: {
						pk_id: state.pk_id,
						gather_way: function () {
							return state.curGatherWay
						},
						task_type: function () {
							return state.task_type
						},
						upload_type: function () {
							return state.upload_type
						},
						source_no: function () {
							return state.source_no
						},
					}
					,method: "POST"
					,before: function(obj){

					}
					,done: function(res){
						console.log('res===',res);
						//layer.load(); //上传loading 32000
						if (200 == res.code) {
							getAndSaveCheckResultToGlobal(res.data,'bulk_box')
							summaryTable("#bulk_check_normal_result_summary",res.data.normal_summary)
							renderBulkData()
						} else {
							layer.alert(res.msg)
							return false
						}
					}
				});

				function submit(uploadType = 0) {
					var index = layer.load(1, {
						shade: [0.1,'#fff'] //0.1透明度的白色背景
					});
					$.ajax({
						type: 'post',
						url: "/goodsPacking/add",
						data: {
							pk_id: state.pk_id,
							task_type: state.task_type,
							box_no: state.boxNo,
							lead_seal_no: state.leadSealNo,
							source_no: state.source_no,
							gather_way: function () {
								return state.curGatherWay
							},
							upload_type: uploadType
							// goods_codes: goodsCodes
						},
						success: function (res) {
							console.log('装箱返回结果：',res)
							layer.close(index)
							if (res.code==200) {
								console.log("装箱提交成功",res)
								// 真实关闭RFID服务
								realStopAndDisconnect()
                                // 只有调拨任务才打印箱码
                                if(state.task_type == 1) {
                                    // 箱码打印
                                    let pkIds = res.data.pk_ids
                                    printBoxNo(state.source_no,pkIds)
                                }

                                // 装箱完成后弹出漏装箱列表
                                showNotBoxListModal(state.source_no, function() {
                                    // 关闭当前页面
                                    closeTools.layer_iframe_close(350, true);
                                });
								return false
							} else {
								layer.msg(res.msg,{icon:2,time:1500})
							}
						}
					})
				}

                function printBoxNo(source_no,pk_ids) {
                    $.post('/goodsPacking/getBoxNoPrintInfo', {
                        source_no, pk_ids
                    }, function (r) {
                        console.log('获取箱码打印信息',r)
                        if (r.code === 200) {
                            for (let i = 0; i < r.data.length; i++) {
                                let item = r.data[i]
                                console.log('箱码打印信息',item)
                                // 调拨装箱自动打印箱码仓库门店全放开-240131
                                allotBoxPrint([{
									allot_type: item.allot_type,//仓库类型
                                    source_no: item.source_no,//调拨单号
                                    in_w_id: item.in_w_id,//调入仓ID
                                    in_w_name: item.in_w_name,//调入仓库名称
                                    brand_name_tips: item.brand_names,//品牌
                                    pk_num: item.goods_num,//件数
                                    ask_date: item.show_at,//期望到店时间
                                    pk_admin_name: item.pk_admin_name,//装箱人
                                    pk_time: item.pk_time,//装箱时间
                                    weight: item.weight && item.weight != 0 ? item.weight : 0,//重量
                                    box_no: item.box_no,//箱号
                                    title: getAllotTitle(item.remark)
                                }]);
                            }

                        } else {
                            console.log('箱码打印异常====',r)
                            layer.msg(r.msg);
                        }
                        layer.close(index)
                    });
                }

                function getAllotTitle(title) {
                    console.log('title====',title)
                    // 如果调拨备注里有“批量生成”四个字，则提取这四个字之前的文字作为标题，否则标题默认为空
                    let pos = title.indexOf('批量生成')
                    if (pos > -1) {
                        title = title.substring(0, pos);
                    } else {
                        title = ''
                    }
                    return title
                }

				// exportGpCheckResult
				// 导出异常数据
				$('#exportUniqueCode').on('click',function () {
					exportCheckResult()
				});
				$('#exportBarcode').on('click',function () {
					exportCheckResult()
				});
				$('#exportEpcCode').on('click',function () {
					exportCheckResult()
				});
				$('#exportBulk').on('click',function () {
					exportCheckResult()
				});
				function exportCheckResult() {
					console.log('exportGpCheckResult====')
					// 开启加载
					var index = layer.load(1, {
						shade: [0.1,'#fff'] //0.1透明度的白色背景
					});

					$.post('/goodsPacking/getBulkCheckResult', {
						gather_way: function () {
							return state.curGatherWay
						},
						task_type: function () {
							return state.task_type
						},
						upload_type: function () {
							return state.upload_type
						},
						source_no: function () {
							return state.source_no
						},
						export: 1
					}, function (r) {
						if (r.code === 200) {
							window.open(r.data.url);
							layer.msg(r.msg);
						} else {
							console.log('异常====',r)
							layer.msg(r.msg);
						}
						layer.close(index)
					});
					return false;
				}

				// 批量上传校验结果列表，tab切换触发事件
				// element.on('tab(checkResult)', function(data){
				// 	if (1 === data.index) {
				// 		summaryTable("#bulk_check_normal_result_summary")
				// 	}
				// });

				/**********************************************************环形进度条-start**************************************************/
				// 1.创建canvas元素
				var mycanvas = document.getElementById('mycanvas');
				var ctx = mycanvas.getContext('2d');

				/**
				 * 2. 绘制之前需要做一些准备工作
				 * 	1. 找到“画布的中心点”的，进度条
				 	2. 将进度条按照进度的比例分成100份，按照100%完成
				 	3. 指定初始加载步长（长度），注意这是初始化后期可以改成0
				 * */
				//找到画布的中心点
				var canvasX = mycanvas.width / 2;
				var canvasY = mycanvas.height / 2;
				//进度条是100%，所以要把一圈360度分成100份
				var progress = Math.PI * 2 / 100;
				//指定初始加载步长
				var steps = 0;

				//初始调用动画函数
				progressAnimate(0,'即将开始');
				//动画函数 num=当前进度条所对应具体数值，不是百分比
				function progressAnimate(num,text) {
					//执行平滑动画
					window.requestAnimationFrame(function () {
						//判断步子最终走多远的边界值，此值可以改
						if (steps < num) {
							//该函数在边界内可以调用
							progressAnimate(num,text);
						}
					});
					//清空绘制内容
					ctx.clearRect(0, 0, mycanvas.width, mycanvas.height);
					//每次增加的步长，数值越大步子越大跑的越快，数值越小走的越慢
					if (num > 0) {
						steps += 1;// 步长，随着阈值大小等比例扩大，以便进度条速度适中
					} else if (num === 0) {
						steps = 0
					}
					//调用绘制形状函数，传入参数绘制对象，环形进度步长
					DrawShape(ctx,num,text);
				}

				function DrawShape(ctx,num,text) {
					// 3. 绘制环形底层
					// 先把进度的环形底层浅灰色的环绘制出来，它是进度的路径。可以先把绘制的颜色和线宽指定好，这两个属性对下面的方法顺序起不到影响。
					ctx.strokeStyle = '#dddddd';
					ctx.lineWidth = 20;
					ctx.save();
					ctx.beginPath();
					ctx.arc(canvasX, canvasY, 90, 0, Math.PI * 2, false)
					ctx.stroke();
					ctx.closePath();
					ctx.restore();

					// 4. 绘制进度层
					// 进度层绘制的颜色需要定义出来，另外进度条的粗细与底层环形的粗细相同。这里最重要的一句是，在结束角度的时候加入了“steps progress” 步长 进度。
					// steps 数值越小乘成数之后增加的角度就少，steps数值大乘数之后进度增加的就多。
					ctx.strokeStyle = "#47cab0";
					ctx.lineWidth = 20;
					ctx.save();
					ctx.beginPath();
					ctx.arc(canvasX,canvasY,90, -Math.PI/2, -Math.PI/2+steps*progress,false);
					ctx.stroke();
					ctx.closePath();
					ctx.restore();

					// 5. 绘制字体并指定位置
					// 环形的进度百分比文字显示需要使用canvas的文字绘制，这里需要注意数字是从1位到3位的跨度，还要加入%，因此位置需要变化。当数字到100时文字占宽就更大因此要改变绘制起点。
					ctx.fillStyle = "#000000"; //可改
					ctx.font = "bold 26px Arial"; //可改
					ctx.save();
					// canvasX-30, canvasY+10  中的加减的数值可改
					// 计算num的宽度
					const { width:numWidth } = ctx.measureText(num)
					ctx.fillText(num, canvasX - (numWidth/2), canvasY + 10);
					ctx.font = "17px Arial"; //可改
					const { width:txtWidth } = ctx.measureText(text)
					ctx.fillText(text, canvasX - (txtWidth/2), canvasY + 37);
					ctx.restore();

				}

				function setProgressText(text) {

					ctx.font = "17px Arial"; //可改
					const {width:clearWidth } = ctx.measureText('正在扫描...') // 以最宽的字数宽度掩盖
					const {width:txtWidth } = ctx.measureText(text)
					// 先清空之前的文本
					// ctx.fillStyle = 'red'
					// ctx.fillRect(canvasX - (txtWidth/2),canvasY + 20,txtWidth,22);
					ctx.clearRect(canvasX - (clearWidth/2),canvasY + 20,clearWidth,22) // 四个字的矩形框宽度

					// 填充新的文本
					ctx.fillText(text, canvasX - (txtWidth/2), canvasY + 37);
					ctx.restore();
				}

				/**********************************************************环形进度条-end**************************************************/


				/**********************************************************RFID-start**************************************************/
				////////////////////////////////////////////////////////以下为应用方法/////////////////////////////////////////////////
				function controlRFID() {
					// 未真实关闭
					// localStorage.setItem("isRealDisConnected",'0')
					state.isRealDisConnected = false
					// 根据开关状态控制RFID开关
					if (state.isStarted ) { // 开启
						console.log('epc：开启');
						// 开启RFID并持续读取
						connectAndRead()
					} else { // 关闭
						console.log('epc：关闭');
						stopAndDisconnect()
					}
				}

				////////////////////////////////////////////////////////以下为基础方法/////////////////////////////////////////////////
				function connectAndRead () {
					// 链接并启动
					connectAndStart()
					// 持续读取
					continueRead()

					// let isRealDisConnected = localStorage.getItem("isRealDisConnected")
					// console.log('connectAndRead-是否真实断开=',isRealDisConnected)
					// // 真实断开了才连接
					// if (isRealDisConnected === '1') {
					// 	// 链接并启动
					// 	connectAndStart()
					// } else {
					// 	console.log('connectAndRead-未真实断开-不连接=')
					// }
					//
					// // 持续读取
					// continueRead()
				}

				let timer;
				function stopAndDisconnect() {
					clearInterval(timer);
					// curlPost(BASE_URL + '/api/Reader/stopAndDisconnect')
				}

				// 真实关闭服务并关闭定时器
				realStopAndDisconnect()
				function realStopAndDisconnect() {
					console.log('是否真实关闭RFID服务=',state.isRealDisConnected)
					// 未真实关闭时，关闭服务
					if (!state.isRealDisConnected) {
						console.log('真实关闭服务并关闭定时器...')
						clearInterval(timer);
						curlPost(BASE_URL + '/api/Reader/stopAndDisconnect',{type:'realStopAndDisconnect'})
					}
				}

				function connectAndStart() {
					// 未真实关闭
					state.isRealDisConnected = false
					curlPost(BASE_URL+ '/api/Reader/connectAndStart',{type: 'connectAndStart'});
				}

				function continueRead() {
					timer=setInterval(Read,1500);
				}

				function Read() {
					curlPost(BASE_URL + '/api/Reader/continueRead',{type: 'Read'});
				}

				function curlPost(url,params) {
					console.log(url);
					$.ajax({
						url: url,
						type: 'POST',
						data:{StartReadParam:{MemoryBank:'EPC',ExcludeDuplication:'true'}},
						dataType: 'JSON',
						async: false,
						success: function (result) {
							if ('boolean' != typeof(result)) {
								// 将返回结果提取，去重
								let epcRes = array_unique(array_column(result.Data,'Epc'))
								// 将读取的结果向全局数组push
								state.goodsCodes.push.apply(state.goodsCodes,epcRes) // 合并数组
								// console.log('去重前的数组goodsCodes==',state.goodsCodes)
								let total1 = state.finalEpcCodes.length
								state.finalEpcCodes = array_unique(state.goodsCodes)
								let total2 = state.finalEpcCodes.length
								// console.log('去重后的新数组==',state.finalEpcCodes)
								if (total2 !== total1) {
									progressAnimate(total2,'正在扫描...')
								}
							}
						},
						error: function (error) {
							console.log(typeof (error));
							console.log('epc请求结果fail==',error);
							layer.msg('设备读取EPC码失败，暂停后，请重新启动RFID驱动服务',{icon:2,time:1500})
							//$("#goods_codes").text(JSON.stringify(error));
						}
					});
				}

				/**********************************************************RFID-end**************************************************/

				/**********************************************************修改array原型-start**************************************************/
				// Object.create返回一个新对象，而来新对象的__proto__就是传进去的参数。
				// let newPrototype = Object.create(Array.prototype);
				// // 然后可以在新原型上添加同名的方法就可以了
				// newPrototype.push = function(...args) {
				// 	// 语义化this
				// 	let curArr = this;
				// 	// 最后还是会执行原始的push
				// 	return Array.prototype.push.call(curArr, ...args);
				// };
				// newPrototype.concat = function(arr) {
				// 	// 语义化this
				// 	let curArr = this;
				// 	console.log("使用了concat");
				// 	// 最后还是会执行原始的push
				// 	progressAnimate()
				// 	return Array.prototype.concat.call(curArr, arr);
				// };

				/**********************************************************修改array原型-end**************************************************/

				/**********************************************************基础方法-start**************************************************/
				// 数组去重
				function array_unique (arr) {
					return Array.from(new Set(arr))
				}

				//获取数组某一列成组成新数组
				function array_column(arr,field) {
					arr = Array.from(arr); // 将arr用Array.from()方法转换为数组
					const arrNew = arr.map(item => {
						return item[field]
					});
					return arrNew
				}
				/**********************************************************基础方法-end**************************************************/

			})
		</script>

	</body>
</html>
