<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <script src="/static/js/jquery.min.js"></script>
</head>
<style>
    .clear{ clear:both}
    .uploader-list {
        margin-left: -15px;
    }

    .uploader-list .info {
        position: relative;
        margin-top: -25px;
        background-color: black;
        color: white;
        filter: alpha(Opacity=80);
        -moz-opacity: 0.5;
        opacity: 0.5;
        width: 100px;
        height: 25px;
        text-align: center;
        display: none;
    }

    .uploader-list .handle {
        position: relative;
        background-color: black;
        color: white;
        filter: alpha(Opacity=80);
        -moz-opacity: 0.5;
        opacity: 0.5;
        width: 100px;
        text-align: right;
        height: 18px;
        margin-bottom: -18px;
        display: none;
    }

    .uploader-list .handle span {
        margin-right: 5px;
    }

    .uploader-list .handle span:hover {
        cursor: pointer;
    }

    .uploader-list .file-iteme {
        margin: 12px 0 0 15px;
        padding: 1px;
        float: left;
    }

</style>

<body class="pear-container">
<div class="layui-card">
    <hr class="layui-bg-gray">
    <div class="layui-card-body">
        <div class="layui-row" style="padding: 20px;">
            <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                <div class="layui-row">
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >调拨单号:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['serial_no']}}
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >发货仓库:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['out_w_name']}}
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >状态:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['status_name']}}
                        </div>
                    </div>
                    <div class="layui-col-xs6 layui-col-sm6 layui-col-md12">
                        <label class="layui-form-label" >创建时间:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['created_at']}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                <div class="layui-row">
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >收货仓库:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['in_w_name']}}
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >收货人:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['linkman']}}
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >收货时间:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['in_at']}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                <div class="layui-row">
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label" >运费承担:</label>
                        <div class="layui-input-inline" style="line-height: 36px;">
                            {{$info['payer']}}
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label layui-col-sm4" style="width: 80px !important;" >收货信息:</label>
                        <div class="layui-input-inline layui-col-sm8" style="padding-top: 8px;">
                            {{$info['linkman']}},{{$info['phone']}},{{$info['address']}}
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                        <label class="layui-form-label layui-col-sm4" style="width: 80px !important;" >备注信息:</label>
                        <div class="layui-input-inline layui-col-sm8" style="padding-top: 8px;">
                            {{$info['remark']??'无'}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <hr class="layui-bg-gray">
    <div class="layui-tab" lay-filter="tab-all">
        <ul class="layui-tab-title">
            <li class="layui-this">货品信息</li>
            <li>货品装箱</li>
            <li lay-id="23">装箱质检</li>
            <li>物流发货</li>
            <li>货品签收</li>
            <li>货品入库</li>
            <li>货品差异</li>
            <li>操作记录</li>
        </ul>

        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-row">
                            <input type="hidden" name="id" value="{{$info['id']}}">
                            <div class="layui-col-md5">
                                <div class="layui-input-inline">
                                    <select name="search_type">
                                        <option value="">全部货品</option>
                                        <option value="1">出库有差异</option>
                                        <option value="2">发货有差异</option>
                                        <option value="3">到货有差异</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline">
                                    <input type="text" name="content" placeholder="店内码或条形码"  autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-btn-group">
                                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query_wait">
                                        <i class="layui-icon layui-icon-search"></i>
                                        查询
                                    </button>
                                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export">
                                        <i class="layui-icon layui-icon-export"></i>
                                        导出
                                    </button>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-btn-group layui-layout-right">
                                    @if(in_array($info['status'],[2,4]) && in_array($info['out_w_id'],$wIds))
                                        <button type="button" class="pear-btn pear-btn-md pear-btn-primary create_btn" data-serial_no="{{$info['serial_no']}}" >生成出库</button>
                                    @endif
                                    @if(in_array($info['status'],[2,4,5]) && in_array($info['out_w_id'],$wIds))
                                        <button type="button" class="pear-btn pear-btn-md pear-btn-primary cancel_btn" data-id="{{$info['id']}}" >作废调拨</button>
                                    @endif
                                    @if($info['status'] == 7 && in_array($info['in_w_id'],$wIds))
                                        <button type="button" class="pear-btn pear-btn-md pear-btn-primary  finish_btn" data-id="{{$info['id']}}">完成调拨</button>
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <table class="layui-hide" id="goods_list"></table>
            </div>

            <div class="layui-tab-item">
                <div class="h_btwn h-vc">
                    <form class="layui-form" style="display:inline-block;width:50%" action="" method="post">
                        <div class="layui-form-item">
                            <div class="layui-input-inline">
                                <select name="pk_admin_id" id="pk_admin_id" lay-search></select>
                            </div>
                            <div class="layui-input-inline">
                                <input name="box_no" id="box_no" type="text" placeholder="请输入箱号" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width: 20%">
                                <input type="checkbox" name="print_times" value="0" title="未打印" lay-skin="primary" lay-filter="unprinted">
                            </div>
                            <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit=""
                                    lay-filter="box_query">
                                筛选
                            </button>
                            <button type="reset" class="mlr-s pear-btn pear-btn-md layui-btn layui-btn-primary">重置</button>

                            <button class="pear-btn pear-btn-md layui-btn-primary" id="box_down">下载</button>
                        </div>
                    </form>
                    <div style="float: right;" class="h-hvc">
                        <button id="box_print" class="layui-btn layui-btn-normal">箱码打印</button>
                        @if($info['status'] !== -1)
                            @if(in_array($info['status'],[4,5,6,7]) && in_array($info['out_w_id'],$wIds))
                                <div class="layui-input-inline ml8">
                                    <input id="box_btn" type="text" class="layui-input" placeholder="箱号扫描" />
                                </div>
                            @endif
                        @endif
                    </div>
                </div>
                <div style="padding-top: 4%;">
                    <div class="row h_btwn">
                        <div>
                            <button class="layui-btn" id="exportGpSelectDetail" style="margin: 5px">
                                <i class="bold layui-icon layui-icon-download-circle"></i>
                                下载明细
                            </button>
                            <button class="layui-btn" id="batchPrint" style="margin: 5px">
                                <i class="bold layui-icon layui-icon-print"></i>
                                批量打印
                            </button>
                            <button class="layui-btn pear-btn-primary" id="batchWeight" style="margin: 5px">
                                批量称重绑铅封
                            </button>
                            <button class="layui-btn pear-btn-primary" id="notBoxList" style="margin: 5px">
                                漏装箱记录
                            </button>
                        </div>
                        <div class="row h-hvc">
                            <div>已装箱货品总数：<span id="goods_total">0</span>件</div>
                        </div>
                    </div>
                    <table lay-data="{id: 'dataTable2'}" class="dataTable2" id="dataTable2" lay-filter="dataTable2"></table>
                </div>
            </div>

            <div class="layui-tab-item">
                <div class="layui-form">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                        <legend>质检扫描区</legend>
                    </fieldset>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md5">
                            <div class="grid-demo grid-demo-bg1">
                                <form class="layui-form" action="" method="post">
                                    <div class="layui-inline">
                                                <textarea placeholder="请扫描箱号进行发货前的货品检查" name="boxNos" id="boxNos"
                                                          class="layui-textarea" rows="6"
                                                          cols="32"></textarea>
                                    </div>
                                    <div class="layui-inline">
                                        <button class="pear-btn pear-btn-md pear-btn-primary"
                                                style="height: 130px;width: 100px;" lay-submit
                                                lay-filter="box_confirm">
                                            <p style="padding: 0;height: 17px;">已扫描</p>
                                            <font id="already_box_num">0</font>
                                            <p style="padding: 0;">确定</p>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="layui-col-md7">
                            <div class="grid-demo">
                                <lable style="font-size: 14px;padding-left: 3%;">扫描（<font
                                            id="saomiao">0</font>）
                                </lable>&nbsp;&nbsp;&nbsp;&nbsp;
                                <lable style="font-size: 14px;padding-left: 3%;">异常（<font
                                            id="yichang">0</font>）
                                </lable>
                                <table id="dataTableBox" lay-filter="dataTableBox"></table>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs6 layui-col-md12">
                            <div class="grid-demo grid-demo-bg2">
                                <table id="dataTableBoxList" lay-filter="dataTableBoxList"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-tab-item">
                <div class="layui-row layui-col-space10">
                    <div class="grid-demo grid-demo-bg1">
                        <form class="layui-form" action="" method="post">
                            <div class="layui-inline layui-col-md2">
                                                <textarea placeholder="请输入箱号" name="boxDeliveryNos" id="boxDeliveryNos"
                                                          class="layui-textarea" rows="2"
                                                          style="min-height: 34px;height: 34px;"
                                                          cols="32"></textarea>
                            </div>
                            <div class="layui-inline" style="width: 80.8%">
                                <button class="layui-btn  pear-btn-md layui-btn-normal" lay-submit
                                        lay-filter="box_delivery_confirm">筛选
                                </button>
                                @if(in_array($info['status'],[4,5,6,7]) && in_array($info['out_w_id'],$wIds))
                                <button class="pear-btn pear-btn-danger pear-btn-md" style="float: right;"
                                        id="box_delivery_void" lay-filter="box_delivery_void">作废
                                </button>
                                @endif
                            </div>
                        </form>
                    </div>
                </div>
                <table id="dataTableBoxDeliveryList" lay-filter="dataTableBoxDeliveryList"></table>

            </div>

            <div class="layui-tab-item">
                <div class="layui-tab" lay-filter="switchSignType">
                    <ul class="layui-tab-title">
                        <li class="layui-this" sign-type="1">店内码</li>
                        <li sign-type="3">RFID</li>
                        <li sign-type="2">条形码</li>
                        <li sign-type="4">批量签收</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="layui-form">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md5">
                                        <div class="grid-demo grid-demo-bg1">
                                            <form class="layui-form" action="" method="post">
                                                <div class="layui-input-inline">
                                                    <input type="text" placeholder="请扫描箱号" name="sign_uni_box" id="sign_uni_box" class="layui-input">
                                                <textarea placeholder="请扫描店内码一行一个" name="uniqueCodeInput" id="uniqueCodeInput"
                                                          class="layui-textarea" rows="6"
                                                          cols="32"></textarea>
                                                </div>
                                                <div class="layui-inline">
                                                    <button class="pear-btn pear-btn-md pear-btn-primary"
                                                            style="height: 130px;width: 100px;" lay-submit
                                                            lay-filter="unicode_confirm">
                                                        <p style="padding: 0;height: 17px;">已扫描</p>
                                                        <font id="already_uni_num">0</font>
                                                        <p style="padding: 0;">确定</p>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="layui-col-md7">
                                        <div class="grid-demo">
                                            <lable style="font-size: 14px;padding-left: 3%;">扫描（<font
                                                        id="uni_saomiao">0</font>）
                                            </lable>&nbsp;&nbsp;&nbsp;&nbsp;
                                            <lable style="font-size: 14px;padding-left: 3%;">异常（<font
                                                        id="uni_yichang">0</font>）
                                            </lable>
                                            <table id="uniqueCodeErrorList" lay-filter="uniqueCodeErrorList"></table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-tab-item">
                            <div class="layui-form">
                                <div class="layui-form">
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-md5">
                                            <form class="layui-form" action="" method="post">
                                                <div class="layui-input-inline">
                                                    <input type="text" placeholder="请扫描箱号" name="sign_epc_box" id="sign_epc_box" class="layui-input">
                                                    <button class="pear-btn pear-btn-md pear-btn-primary" type="button"
                                                            style="height: 120px;
                                                                        width: 120px;
                                                                        background-color: white !important;
                                                                        color: black !important;
                                                                        border-radius: 60px 60px 60px 60px;
                                                                        border-color: #2dc4f0;
                                                                        border-width: 10px;
                                                                        margin-top: 10px;
                                                                        margin-left: 25px;
                                                            " id="rfid_confirm">
                                                        <font id="already_rfid_num">0</font>
                                                        <p style="padding: 0;" id="rfid_label">即将开始</p>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="layui-col-md7">
                                            <div class="grid-demo">
                                                <lable style="font-size: 14px;padding-left: 3%;">扫描（<font
                                                            id="rfid_saomiao">0</font>）
                                                </lable>&nbsp;&nbsp;&nbsp;&nbsp;
                                                <lable style="font-size: 14px;padding-left: 3%;">异常（<font
                                                            id="rfid_yichang">0</font>）
                                                </lable>
                                                <table id="rfidErrorList" lay-filter="rfidErrorList"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-tab-item">
                            <div class="layui-form">
                                <div class="layui-form">
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-input-inline">
                                                    <input type="text" placeholder="请扫描条码" name="barcodeInput" id="barcodeInput" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md8">
                                            <div class="layui-inline">
                                                <table id="scanBarcodeList" lay-filter="scanBarcodeList"></table>
                                            </div>
                                            <div class="layui-inline">
                                                <button class="pear-btn pear-btn-md pear-btn-primary"
                                                        style="height: 130px;width: 100px;" id="barcode_confirm">
                                                    <p style="padding: 0;">确定</p>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-md12">
                                            <div class="grid-demo">
                                                <lable style="font-size: 14px;padding-left: 3%;">扫描（<font
                                                            id="bar_saomiao">0</font>）
                                                </lable>&nbsp;&nbsp;&nbsp;&nbsp;
                                                <lable style="font-size: 14px;padding-left: 3%;">异常（<font
                                                            id="bar_yichang">0</font>）
                                                </lable>
                                                <table id="barcodeErrorList" lay-filter="barcodeErrorList"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-tab-item">
                            <div class="layui-form">
                                <div class="layui-form">
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-md5">
                                            <div class="grid-demo grid-demo-bg1">
                                                <form class="layui-form" id="upload_form" action="" enctype="multipart/form-data">
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">签收方式</label>
                                                        <div class="layui-input-block">
                                                            <input type="radio" name="code_type" lay-filter="code_type" value="1" title="店内码" ><div class="layui-unselect layui-form-radio layui-form-radioed"><i class="layui-anim layui-icon"></i><div>店内码</div></div>
                                                            <input type="radio" name="code_type" lay-filter="code_type" value="2" title="条形码" ><div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i><div>条形码</div></div>
                                                        </div>
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <div class="layui-row">
                                                            <label class="layui-form-label">入库数据</label>
                                                            <input type="hidden" name="source_no" value="{{$info['serial_no']}}">
                                                            <div class="layui-col-md5">
                                                                <div class="layui-input-inline">
                                                                    <input type="file" id="file" name="file" lay-verify="required" class="layui-input" >
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-btn-group">
                                                                    <button class="pear-btn pear-btn-md pear-btn-primary" data-id="{{$info['id']}}" id="sign_import_btn" lay-submit lay-filter="import">
                                                                        上传
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">导入模板</label>
                                                        @foreach($tpl_list as $item)
                                                            <div class="layui-input-block">
                                                                <a href="{{$item['down_url']}}" target="_blank">{{$item['title']}}</a> <span>文件格式：{{$item['remark']}} </span>
                                                            </div>
                                                        @endforeach

                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        <div class="layui-col-md7">
                                            <div class="grid-demo">
                                                <lable style="font-size: 14px;padding-left: 3%;">上传（<font
                                                            id="file_saomiao">0</font>）
                                                </lable>&nbsp;&nbsp;&nbsp;&nbsp;
                                                <lable style="font-size: 14px;padding-left: 3%;">异常（<font
                                                            id="file_yichang">0</font>）
                                                </lable>
                                                <table id="fileErrorList" lay-filter="fileErrorList"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-xs6 layui-col-md12">
                        <div class="grid-demo grid-demo-bg2">
                            <table id="signBoxList" lay-filter="signBoxList"></table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-tab-item">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-row" style="height: 34px;">
                            <div class="layui-col-md5 layui-col-md-offset7">
                                <div class="layui-btn-group layui-layout-right">
                                    @if($info['status'] !== -1)
                                        @if(in_array($info['status'],[6,7]) && in_array($info['in_w_id'],$wIds) && empty($diffInfo["id"]))
                                            <button type="button" class="pear-btn pear-btn-md pear-btn-primary in_btn" data-serial_no="{{$info['serial_no']}}" data-id="{{$outInfo['id']}}" >新建</button>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <div id="in_list_panel">
                    <table class="layui-hide" lay-filter="in_list" id="in_list"></table>
                </div>

                <form class="layui-form" id="in_panel" style="display: none" action="" method="post">
                    <input type="hidden" name="allot_id" value="{{$info['id']}}">
                    <div class="layui-card-body">
                        货品信息：任务数：{{$outInfo['total_num'] ?? 0}} 出库数：{{$outInfo['out_num'] ?? 0}}
                        已发货数：{{$outInfo['send_num'] ?? 0}}
                    </div>

                </form>

            </div>

            <div class="layui-tab-item ">
                <div class="layui-tab" lay-filter="tab-diff">
                    <ul class="layui-tab-title">
                        <li class="layui-this" id="diff_tab_btn">差异列表</li>
                        <li >差异处理列表</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item">
                            <form class="layui-form" action="">
                                <div class="layui-form-item">
                                    <div class="layui-row">
                                        <input type="hidden" name="diff_id" value="{{$diffInfo["id"] ?? 0}}">
                                        <div class="layui-col-md5">
                                            <div class="layui-input-inline">
                                                <select name="search_type" lay-filter="search_type">
                                                    <option value="">请选择查询方式</option>
                                                    <option value="1" selected="">店内码查询</option>
                                                    <option value="3">条形码查询</option>
                                                    <option value="5">SKU查询</option>
                                                </select>
                                            </div>
                                            <div class="layui-input-inline">
                                                <input type="text" id="search_value" name="search_value" autocomplete="off" class="layui-input">
                                            </div>
                                        </div>
                                        <div class="layui-col-md3">
                                            <div class="layui-btn-group">
                                                <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query_diff">
                                                    <i class="layui-icon layui-icon-search"></i>
                                                    查询
                                                </button>
                                                <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export_diff">
                                                    <i class="layui-icon layui-icon-export"></i>
                                                    导出
                                                </button>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-btn-group layui-layout-right">
                                                @if(in_array($info['in_w_id'],$wIds) && !empty($diffInfo["id"]))
                                                    <button type="button" class="pear-btn pear-btn-md pear-btn-primary  finish_diff_btn" data-id="{{$diffInfo["id"] ?? 0}}">差异处理完成</button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <table id="diff_list" lay-filter="diff_list"></table>
                        </div>
                        <div class="layui-tab-item">
                            <form class="layui-form" action="">
                                <div class="layui-form-item">
                                    <div class="layui-row">
                                        <input type="hidden" name="diff_id" value="{{$diffInfo["id"] ?? 0}}">
                                        <div class="layui-col-md5">
                                            <div class="layui-input-inline">
                                                <select name="search_type" lay-filter="search_type">
                                                    <option value="">请选择查询方式</option>
                                                    <option value="1" selected="">店内码查询</option>
                                                    <option value="3">条形码查询</option>
                                                    <option value="5">SKU查询</option>
                                                </select>
                                            </div>
                                            <div class="layui-input-inline">
                                                <input type="text" id="search_value" name="search_value" autocomplete="off" class="layui-input">
                                            </div>
                                        </div>
                                        <div class="layui-col-md3">
                                            <div class="layui-btn-group">
                                                <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query_result">
                                                    <i class="layui-icon layui-icon-search"></i>
                                                    查询
                                                </button>
                                                <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export_result">
                                                    <i class="layui-icon layui-icon-export"></i>
                                                    导出
                                                </button>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-btn-group layui-layout-right">
                                                @if(in_array($info['in_w_id'],$wIds) && !empty($diffInfo["id"]))
                                                    <button type="button" class="pear-btn pear-btn-md pear-btn-primary  finish_diff_btn" data-id="{{$diffInfo["id"] ?? 0}}">差异处理完成</button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <table id="handle_list" lay-filter="handle_list"></table>
                        </div>
                    </div>
                </div>

            </div>
            <div class="layui-tab-item">
                <div class="layui-row">
                    <table class="layui-table">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>操作类型</th>
                            <th>操作内容</th>
                            <th>操作人</th>
                            <th>操作时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($log_list as $key => $log)
                            <tr>
                                <td>{{$key+1}}</td>
                                <td>{{$log['req_router_name'] ?? ''}}</td>
                                <td>{{$log['remark'] ?? ''}}</td>
                                <td>{{$log['admin_name'] ?? ''}}</td>
                                <td>{{$log['req_time'] ?? ''}}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
            <div class="clear"></div>
        </div>
    </div>
    <input type="hidden" name="gp_source_no" value="{{$info['serial_no']}}" />
    <input type="hidden" name="allot_remark" value="{{$info['remark']}}" />
</div>
<div id="notBoxTableDiv" style="display: none">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="searchParam" class="layui-form layui-form-pane" action="">
                <div class="layui-row mt-7">
                    <div class="layui-col-xs6">
                        <label class="layui-form-label">拣货人</label>
                        <div class="layui-input-inline" style="width: 60%">
                            <input type="text" name="pick_admin_name" autocomplete="off" class="layui-input" id="pick_admin_name">
                        </div>
                    </div>
                    <div class="layui-col-xs6">
                        <label class="layui-form-label">是否绑定</label>
                        <div class="layui-input-inline" style="width: 60%">
                            <select name="band_admin_id" class="layui-select">
                                <option value=""></option>
                                <option value="1">是</option>
                                <option value="0">否</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div  style="display: flex;justify-content: flex-end;">
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
                            <i class="layui-icon layui-icon-search"></i>
                            查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="notBoxTable" ></table>
</div>
<form id="batchWeightForm" class="layui-form" action="" style="display: none">
    <div class="layui-form-item h-hvc">
        <label class="layui-form-label"><span class="star">*</span>扫描包裹号</label>
        <div class="layui-input-inline">
            <input type="text" id="batch_box_no" name="box_no" placeholder="" class="layui-input">
        </div>
{{--        <div style="color: red">包裹号已存在</div>--}}
    </div>
    <div class="layui-form-item h-hvc">
        <label class="layui-form-label">扫描铅封号</label>
        <div class="layui-input-inline">
            <input type="text" id="batch_lead_seal_no" name="lead_seal_no" placeholder="" class="layui-input">
        </div>
{{--        <div style="color: red">包裹号已存在</div>--}}
    </div>
    <div class="layui-form-item h-hvc">
        <label class="layui-form-label">包裹重量(公斤)</label>
        <div class="layui-input-inline">
            <input type="number" id="batch_weight" name="weight" placeholder="" class="layui-input">
        </div>
{{--        <div style="color: red">包裹号已存在</div>--}}
    </div>
{{--    <div class="layui-form-item">--}}
{{--        <div class="layui-input-block">--}}
{{--            <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>--}}
{{--            <button type="reset" class="layui-btn layui-btn-primary">重置</button>--}}
{{--        </div>--}}
{{--    </div>--}}
</form>
@verbatim
<script type="text/html" id="box_detail_panel">
        <a href="/goodsPacking/detail?id={{ d.id }}" class="pear-btn pear-btn-primary pear-btn-sm" lay-event="pack_detail">查看明细</a>
        {{# if(d.status == 1){ }}
        <a class="pear-btn pear-btn-danger pear-btn-sm" lay-event="pack_cancel">作废</a>
        {{# } }}
        <a class="pear-btn layui-btn-normal pear-btn-sm" lay-event="pack_weight">包裹称重</a>
        <a class="pear-btn layui-btn-normal pear-btn-sm" style="display: {{d.status == 1 ? 'inline-block' : 'none'}}" lay-event="pack_append_bill">追加明细</a>
</script>





<script type="text/html" id="in_detail_panel">
    <a class="pear-btn pear-btn-sm" lay-event="viewIn">查看明细</a>
    {{# if(d.status == 0){ }}
    <a class="pear-btn pear-btn-sm" lay-event="cancelIn">作废</a>
    <a class="pear-btn pear-btn-sm" lay-event="finishIn">完成入库</a>
    {{# } }}
</script>

<script type="text/html" id="send_detail_panel">
    <a class="pear-btn pear-btn-sm" lay-event="view">查看明细</a>
</script>

<script type="text/html" id="diffBtn">
    <a class="layui-btn layui-btn-xs" lay-event="commit">提交</a>
</script>

<script type="text/html" id="resultBtn">
    {{# if(d.decision_id == 0){ }}
    <a class="layui-btn layui-btn-xs" lay-event="cancel">取消</a>
    {{# } }}
</script>

@endverbatim

<script type="text/html" id="toolbarBoxDelivery">
    <div class="">
        <label style="font-size: 14px;margin-left: 3%;">已经质检（<font id="already_out_quality">0</font>）</label>
        <label style="font-size: 14px;margin-left: 3%;">即将质检（<font id="no_quality">0</font>）</label>
        <label style="font-size: 14px;margin-left: 3%;">已经发货（<font id="already_num">0</font>）</label>
        <button class="layui-btn layui-btn-sm layui-btn-primary" style="float: right;margin-left: 1%;"
                lay-event="boxDeliveryDown">下载
        </button>
        @if($info['status'] !== -1)
            @if(in_array($info['status'],[4,5,6,7]) && in_array($info['out_w_id'],$wIds))
                <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" lay-event="expressDelivery">物流发货
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" lay-event="finishDelivery">完成发货
                </button>
            @endif
        @endif
        <button class="layui-btn layui-btn-sm" style="float: right;" lay-event="search">筛选
        </button>
        <div class="layui-col-md2 layui-form" style="float: right">
            <select name="status" id="boxDeliveryStatus">
                <option value="">发货状态</option>
                <option value="1">已装箱</option>
                <option value="3">发货中</option>
                <option value="2">已发货</option>
            </select>
        </div>
        <div class="layui-col-md2" style="float: right">
            <select name="isQuality" id="boxDeliveryQuality">
                <option value="">是否质检</option>
                <option value="1">是</option>
                <option value="0">否</option>
            </select>
        </div>
    </div>
</script>

<script type="text/html" id="toolbarSignBox">
    <div class="">
        <label style="font-size: 14px;margin-left: 3%;">发货数量（<font id="send_goods_num">0</font>）</label>
        <label style="font-size: 14px;margin-left: 3%;">签收数量（<font id="sign_goods_num">0</font>）</label>
        <button class="layui-btn layui-btn-sm layui-btn-primary" style="float: right;margin-left: 1%;"
                lay-event="signBoxDown">下载
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" lay-event="syncEpcCode">同步门店EPC
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" lay-event="unSignList">未签记录</button>
        @if($info['status'] !== -1)
            @if(in_array($info['status'],[6,7]) && in_array($info['in_w_id'],$wIds))
                <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" lay-event="enterSign">确定签收
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" lay-event="signLogList">签收记录
                </button>
            @endif
        @endif
    </div>
</script>

<script type="text/html" id="boxQuality">
    @if(in_array($info['status'],[4,5,6,7]) && in_array($info['out_w_id'],$wIds))
    @verbatim
    {{# if(d.status == 1){ }}
    <a class="layui-btn layui-btn-xs" lay-event="deliveryExpress">物流发货</a>
    {{# } }}
    @endverbatim
    @endif
</script>

<script type="text/html" id="boxDeliveryTool">
    @if(in_array($info['status'],[4,5,6,7]) && in_array($info['out_w_id'],$wIds))
    @verbatim
    {{# if(d.image_list){ }}
    <a class="pear-btn pear-btn-danger pear-btn-sm" lay-event="box_view_img">查看图片</a>
    {{# } }}
    {{# if(d.status == 3){ }}
    <a class="pear-btn pear-btn-danger pear-btn-sm" lay-event="box_delivery_cancel">作废</a>
    {{# } }}
    @endverbatim
    @endif
</script>
<script type="text/html" id="checkbd">
    @verbatim
    {{# if(d.e_type != 1){ }}
    <input type="checkbox" name="check_one" title="" lay-skin="primary" lay-filter='checkone' data-pick_order_detail_id="{{ d.pick_order_detail_id }}" data-pick_order_no="{{ d.pick_order_no }}" data-unique_code="{{ d.unique_code }}" data-e_type="{{ d.e_type }}">
    {{# } }}
    @endverbatim
</script>


<script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
<script src="/static/js/box.js"></script>
<script>
    Date.prototype.Format = function (fmt) { //author: meizz
        var o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }

    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['element', 'table','comm','iframeTools','upload','laydate','tableEdit'], function () {
        // Tab的切换功能，切换事件监听等，需要依赖element模块
        var $ = layui.jquery, table = layui.table,tableEdit = layui.tableEdit, element = layui.element;
        var form = layui.form;
        var layer = layui.layer;
        var upload = layui.upload;
        var laydate = layui.laydate;
        var comm = layui.comm
        var arrival_id = $('#arrival_id').val();
        var iframeTools = layui.iframeTools;
        var serial_no = '{{ $info['serial_no']  }}';
        var allot_id = '{{ $info['id']  }}';
        //异常箱号
        var exceptionBox = {};
        //正常箱号
        var normalBox = {};
        var is_exception = false;
        var is_check = false;

        var saomiao = 0;//扫描数量
        var yichang = 0;//异常数量
        var saomiaoObjData = {};//已扫描记录
        var saomiaoData = [];//已扫描记录
        var saomiaoCols = [[{field: 'box_no', title: '箱号'}, {
            field: 'text',
            title: '错误原因'
        }]];

        var gpData = {
            exportData: [],
            // 当前页数据
            curPageData: [],
            loading: null,
            selectIds: [],
            source_no: $('input[name=gp_source_no]').val(),
            allot_remark: $('input[name=allot_remark]').val(),
            gpWhere: {},
            notBoxSelect:[], // 漏装箱列表：未见实物选中数据
            pick_admin_name: null,
            band_admin_id: null,
        }

        var scanBarCode = new Map();

        var signType = 1;

        $("#file").click(function () {
            $("#file").val('');
            $("#file").change(function(){
                $("#sign_import_btn").click();
            })
        })

        //查询
        form.on('submit(import)', function (data) {
            var formData = new FormData();
            is_diff = false;
            formData.append('source_no', data.field.source_no);
            formData.append('code_type', data.field.code_type);
            formData.append('file', document.getElementById('file').files[0]);
            $.ajax({
                url: '/goodsPacking/fileCheck',
                type: 'post',
                data: formData,
                dataType: "json",
                async: false,//这是重要的一步，防止重复提交的
                cache: false, // 设置为false，上传文件不需要缓存。
                contentType: false, // 设置为false,因为是构造的FormData对象,所以这里设置为false
                processData: false,// 设置为false,因为data值是FormData对象，不需要对数据做处理
                success: function (res) {
                    console.log(res)
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            //刷新表格
                            table.reload('fileErrorList', {
                                data: res.data['error_list']
                            });
                            //赋值数量
                            $("#file_saomiao").html(res.data['total']);
                            $("#file_yichang").html(res.data['error_list'].length);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500})
                    }
                }
            });
            return false;
        });

        //自动处理
        var timer = setInterval(function () {
            var boxNos = $("#boxNos").val();
            if (boxNos) {
                boxNos = $.trim(boxNos).split(/[(\r\n)\r\n]+/);
                if (boxNos.length > 0) {
                    $("#already_box_num").html(boxNos.length);
                }
            } else {
                $("#already_box_num").html(0);
            }
        }, 1000);

        //处理称重输入值
        var Kg = {};
        var weightTimer = setInterval(function () {
            var weight = $(".layui-layer-prompt .layui-layer-input").val();
            if(weight){
                var weights = weight.split(" ");//切割输入的公斤数
                var endWeight = $.trim(weights[weights.length-1]);//获取最后一个
                /*if(!Kg[endWeight]){
                    endWeight = endWeight*2;//公斤转斤
                    endWeight = endWeight.toFixed(1);
                    Kg[endWeight] = true;//记录是否已经处理公斤转斤
                }*/
                $(".layui-layer-prompt .layui-layer-input").val(endWeight);//赋值
            }else{
                $(".layui-layer-input").attr("placeholder","请输入公斤数！");
            }
        },1000);

        function uniqueChangeMonitor(input){
            var uniqueCodes = $(input).val();
            if (uniqueCodes) {
                uniqueCodes = $.trim(uniqueCodes).split(/[(\r\n)\r\n]+/);
                uniqueCodes = $.unique(uniqueCodes);
                if (uniqueCodes.length > 0) {
                    $("#already_uni_num").html(uniqueCodes.length);
                }
                $(this).val(uniqueCodes.join("\n"));
            } else {
                $("#already_uni_num").html(0);
            }
        }

        $("#uniqueCodeInput").keypress(function (e) {
            var code = e.which || e.keyCode;
            if(code == 13){
                uniqueChangeMonitor(this);
            }
        })

        $("#uniqueCodeInput").change(function (e) {
            uniqueChangeMonitor(this);
        })

        function barcodeChangeMonitor(input){
            var barcode = $.trim($(input).val());
            if(!scanBarCode.has(barcode)){
                scanBarCode.set(barcode,{barcode:barcode,num:0});
            }
            var barObj = scanBarCode.get(barcode);

            barObj.num += 1;
            barObj.updated_at = Date.now();
            scanBarCode.set(barcode,barObj);
            var tmepList = [];
            scanBarCode.forEach((v,k)=>{
                tmepList.push(v) ;
            });
            table.reload("scanBarcodeList", {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                initSort:{
                    field:'updated_at',
                    type:'desc'
                },
                data: tmepList
            });

            console.log(tmepList);
            $(input).val('');
        }

        $("#barcodeInput").keypress(function (e) {
            var code = e.which || e.keyCode;
            if(code == 13){
                barcodeChangeMonitor(this)
            }
        })

        //表格初始化 - 装箱信息
        gpList()
        function gpList() {
            var dataTable2 = table.render({
                elem: '#dataTable2'
                , url: "/goodsPacking/getPackList?serial_no={{ $info['serial_no'] }}&task_type=1"
                , page: true //开启分页
                , limit: 100
                , limits: [10, 50, 100]
                , skin: 'line'
                , method: 'post'
                , where: gpData.gpWhere
                ,done: function(res, curr, count){
                    // 配合表头全选获取当页全部数据用
                    if (res.code == 200) {
                        console.log('获取装箱列表=',res.data)
                        gpData.curPageData = res.data
                    }
                }
                , cols: [[ //表头
                    {type: 'checkbox', fixed: 'left'}
                    , {type: 'numbers'}
                    , {field: 'box_no', title: '箱号'}
                    , {field: 'pk_num', title: '货品数'}
                    , {field: 'status_text', title: '状态'}
                    , {field: 'weight', title: '称重'}
                    , {field: 'lead_seal_no', title: '铅封号'}
                    , {field: 'print_times', title: '打印次数'}
                    , {field: 'pk_admin_name', title: '装箱人'}
                    , {field: 'tips', title: '标记', hide: true}
                    , {fixed: 'right', title: '操作', width: 400, align: 'center', toolbar: '#box_detail_panel'}
                ]]
                , defaultToolbar: [{
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
            });
        }
        //打包装箱
        {{--$('#box_btn').click(function () {--}}
        {{--    --}}{{--iframeTools.addTab('货品装箱', "/goodsPacking/add?task_type=1&source_no={{ $info['serial_no'] }}");--}}
        {{--    iframeTools.addTabWithCB('货品装箱', "/goodsPacking/add?flag=add&task_type=1&source_no={{ $info['serial_no'] }}",function (index,url) {--}}
        {{--        console.log(`点击了iframeTools的关闭"X"按钮index=${index},url=${url}`)--}}
        {{--        realStopAndDisconnect()--}}
        {{--    });--}}
        {{--});--}}
        // 真实关闭服务并关闭定时器
        const BASE_URL = 'http://127.0.0.1:30010'
        // 真实关闭服务并关闭定时器
        function realStopAndDisconnect() {
            console.log('外部-强制真实关闭服务并关闭定时器...')
            curlPost(BASE_URL + '/api/Reader/stopAndDisconnect',{type:'realStopAndDisconnect'})
        }

        // 装箱作废
        table.on('tool(dataTable2)', function (obj) {
            var data = obj.data, layEvent = obj.event;
            if (layEvent === 'pack_cancel') {
                layer.confirm('确认作废吗？', function (index) {
                    layer.close(index);
                    var load = layer.load();
                    $.post("/goodsPacking/cancel", {id: data.id}, function (res) {
                        layer.close(load);
                        if (res.code == 200) {
                            layer.msg(res.msg, {icon: 1, time: 1500}, function () {
                                window.location.href = "/allot/detail?id={{ $info['id'] }}";
                            })
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 1500})
                        }
                    });
                });
            }

            if (layEvent === 'pack_weight') {
                layer.prompt({title: '填写包裹重量', formType: 0}, function (weight, index, elem) {
                    layer.close(index);
                    console.log(weight);
                    $.post("/backOrder/pickWeight", {pk_id: data.id,weight:weight}, function (res) {
                        if (res.code == 200) {
                            layer.msg(res.msg, {icon: 1, time: 1500}, function () {
                                Kg = {};
                                tableReload('dataTable2', {'export': 0, 'box_no': ''});
                            })
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 1500})
                        }
                    });
                });
            }

            if (layEvent === 'pack_append_bill') {
                console.log('包裹追加明细')
                iframeTools.addTabWithCB('货品装箱', "/goodsPacking/add?flag=append&pk_id="+data.id+"&box_no="+data.box_no+"&task_type=1&source_no={{ $info['serial_no'] }}",function (index,url) {
                    console.log(`pack_append_bill点击了iframeTools的关闭"X"按钮index=${index},url=${url}`)
                    realStopAndDisconnect()
                });
            }
        });


        //发货记录搜索
        form.on('submit(box_delivery_confirm)', function (data) {
            console.log(data.field);
            data.field.boxDeliveryNos = $.trim(data.field.boxDeliveryNos).split(/[(\r\n)\r\n]+/);
            if (data.field.boxDeliveryNos.length <= 0) {
                layer.msg('请输入箱号！', {icon: 2});
                return false;
            }
            tableReload('dataTableBoxDeliveryList', {'box_no': data.field.boxDeliveryNos});
            return false;
        });
        //质检
        form.on('submit(box_confirm)', function (data) {
            console.log(data.field);
            data.field.boxNos = $.trim(data.field.boxNos).split(/[(\r\n)\r\n]+/);
            if (data.field.boxNos.length <= 0) {
                layer.msg('请输入将要质检的箱号！', {icon: 2});
                return false;
            }
            console.log(data.field);
            $.ajax({
                url: "/goodsPacking/boxQualitys?sourceNo={{ $info['serial_no'] }}",
                type: 'post',
                data: JSON.stringify(data.field),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            //刷新表格
                            tableReload('dataTableBox', {}, saomiaoCols, res.data['error_list']);
                            //赋值数量
                            //$("#already_box_num").html(saomiao);
                            $("#saomiao").html(res.data['total']);
                            $("#yichang").html(res.data['yichang']);

                            //渲染质检列表
                            tableReload('dataTableBoxList');

                            //清空文本域
                            $("#boxNos").val('');
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500})
                    }
                },
                error: function (e) {
                    layer.msg('提交失败!', {icon: 2, time: 1500})
                },
            });
            return false;
        });

        //质检
        form.on('submit(unicode_confirm)', function (data) {
            console.log(data.field);
            var params = {};
            params.box_no = data.field.sign_uni_box; // 店内码类型
            params.code_type = 1; // 店内码类型
            params.code_list = $.trim(data.field.uniqueCodeInput).split(/[(\r\n)\r\n]+/);
            if (params.code_list.length <= 0) {
                layer.msg('请输入将要签收的店内码！', {icon: 2});
                return false;
            }
            console.log(params);
            $.ajax({
                url: "/goodsPacking/checkSignCode?source_no={{ $info['serial_no'] }}",
                type: 'post',
                data: JSON.stringify(params),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            //刷新表格
                            table.reload('uniqueCodeErrorList', {
                                data: res.data['error_list']
                            });
                            //赋值数量
                            $("#uni_saomiao").html(res.data['total']);
                            $("#uni_yichang").html(res.data['error_list'].length);
                            //渲染质检列表
                            // tableReload('dataTableBoxList');
                            //清空文本域
                            // $("#boxNos").val('');
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500})
                    }
                },
                error: function (e) {
                    layer.msg('提交失败!', {icon: 2, time: 1500})
                },
            });
            return false;
        });

        $("#barcode_confirm").click(function () {
            var params = {};
            params.code_type = 2; // 店内码类型
            params.code_list = [];
            scanBarCode.forEach((v,k)=>{
                params.code_list.push(v) ;
            });
            if (params.code_list.length <= 0) {
                layer.msg('请输入将要签收的条码！', {icon: 2});
                return false;
            }
            console.log(params);
            $.ajax({
                url: "/goodsPacking/checkSignCode?source_no={{ $info['serial_no'] }}",
                type: 'post',
                data: JSON.stringify(params),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            //刷新表格
                            table.reload('barcodeErrorList', {
                                data: res.data['error_list']
                            });
                            //赋值数量
                            $("#bar_saomiao").html(res.data['total']);
                            $("#bar_yichang").html(res.data['error_list'].length);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500})
                    }
                },
                error: function (e) {
                    layer.msg('提交失败!', {icon: 2, time: 1500})
                },
            });
            return false;
        })
        //条码确认
        form.on('click(barcode_confirm)', );

        function checkEpc(codeList) {
            var params = {};
            params.code_type = 3; // 店内码类型
            params.box_no = $("#sign_epc_box").val(); // 店内码类型
            params.code_list = codeList;
            if (params.code_list.length <= 0) {
                layer.msg('请采集要签收的RFID！', {icon: 2});
                return false;
            }
            console.log(params);
            $.ajax({
                url: "/goodsPacking/checkSignCode?source_no={{ $info['serial_no'] }}",
                type: 'post',
                data: JSON.stringify(params),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            //刷新表格
                            table.reload('rfidErrorList', {
                                data: res.data['error_list']
                            });
                            //赋值数量
                            $("#rfid_saomiao").html(res.data['total']);
                            $("#rfid_yichang").html(res.data['error_list'].length);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500})
                    }
                },
                error: function (e) {
                    layer.msg('提交失败!', {icon: 2, time: 1500})
                },
            });
            return false;
        }

        function objToArr(objData) {
            var data = [];
            $.each(objData, function (k, v) {
                data.push(v);
            })
            return data;
        }

        function countNum(saomiaoData) {
            //saomiao = saomiaoData.length;
            yichang = 0;
            $.each(saomiaoData, function (k, v) {
                if (v['status'] == 0) {
                    yichang++;
                }
            })
        }

        //图片上传
        var image_arr = new Array();
        upload.render({
            elem: '#test2'
            , url: '/backOrder/upload'
            , multiple: true
            ,before: function(obj){
                layer.msg('图片上传中...', {
                    icon: 16,
                    shade: 0.01,
                    time: 0
                })
            }
            , done: function (res) {
                layer.close(layer.msg());//关闭上传提示窗口
                if (res.code == 200) {
                    $('#uploader-list').append(
                        '<div id="" class="file-iteme">' +
                        '<div class="handle"><span class="glyphicon glyphicon-trash">X</span></div>' +
                        '<img style="width: 100px;height: 100px;" class="upload_img" src="'+ res.data.url +'">' +
                        '</div>'
                    );
                } else {
                    layer.msg(res.msg)
                }
            }
        });
        $(document).on("mouseenter mouseleave", ".file-iteme", function(event){
            if(event.type === "mouseenter"){
                //鼠标悬浮
                $(this).children(".info").fadeIn("fast");
                $(this).children(".handle").fadeIn("fast");
            }else if(event.type === "mouseleave") {
                //鼠标离开
                $(this).children(".info").hide();
                $(this).children(".handle").hide();
            }
        });
        // 删除图片
        $(document).on("click", ".file-iteme .handle", function(event){
            $(this).parent().remove();
        });

        var myDate = new Date();
        console.log(myDate.Format("yyyy-MM-dd hh:mm:ss") );
        //日期时间选择器
        laydate.render({
            elem: '#express_time',
            value:myDate.Format("yyyy-MM-dd hh:mm:ss") ,
            min:myDate.toLocaleString() ,
            type: 'datetime',
            trigger: 'click'
        });
        // 发货登记-提交
        form.on('submit(add_logistics)', function (data) {
            // 获取 多图片
            $('.upload_img').each(function () {
                image_arr.push($(this).attr('src'));
            })
            data.field.image = image_arr;
            console.log(data.field);
            $.ajax({
                url: "/backOrder/logisticsAdd?id={{ $info['id'] }}",
                type: 'post',
                data: JSON.stringify(data.field),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    if (data.code == 200) {
                        layer.alert(data.msg, {
                            time: 3000,
                            end: function () {
                                window.location.href = "/allot/detail?id={{ $info['id'] }}";
                            }
                        });
                    } else {
                        layer.alert(data.msg)
                    }
                },
                error: function (e) {
                    layer.alert("提交失败！")
                },
            });
            return false;
        });

        //发货记录搜索
        form.on('submit(box_delivery_confirm)', function (data) {
            console.log(data.field);
            data.field.boxDeliveryNos = $.trim(data.field.boxDeliveryNos).split(/[(\r\n)\r\n]+/);
            if (data.field.boxDeliveryNos.length <= 0) {
                layer.msg('请输入箱号！', {icon: 2});
                return false;
            }
            tableReload('dataTableBoxDeliveryList', {'box_no': data.field.boxDeliveryNos});
            return false;
        });
        //质检
        form.on('submit(box_confirm)', function (data) {
            console.log(data.field);
            data.field.boxNos = $.trim(data.field.boxNos).split(/[(\r\n)\r\n]+/);
            if (data.field.boxNos.length <= 0) {
                layer.msg('请输入将要质检的箱号！', {icon: 2});
                return false;
            }
            console.log(data.field);
            $.ajax({
                url: "/goodsPacking/boxQualitys?sourceNo={{ $info['serial_no'] }}",
                type: 'post',
                data: JSON.stringify(data.field),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            //刷新表格
                            tableReload('dataTableBox', {}, saomiaoCols, res.data['error_list']);
                            //赋值数量
                            //$("#already_box_num").html(saomiao);
                            $("#saomiao").html(res.data['total']);
                            $("#yichang").html(res.data['yichang']);

                            //渲染质检列表
                            tableReload('dataTableBoxList');

                            //清空文本域
                            $("#boxNos").val('');
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500})
                    }
                },
                error: function (e) {
                    layer.msg('提交失败!', {icon: 2, time: 1500})
                },
            });
            return false;
        });

        function objToArr(objData) {
            var data = [];
            $.each(objData, function (k, v) {
                data.push(v);
            })
            return data;
        }

        function countNum(saomiaoData) {
            //saomiao = saomiaoData.length;
            yichang = 0;
            $.each(saomiaoData, function (k, v) {
                if (v['status'] == 0) {
                    yichang++;
                }
            })
        }

        //筛选箱号
        form.on('submit(box_query)', function (data) {
            console.log('筛选：',data.field);
            // tableReload('dataTable2', {'search': data.field});
            gpData.gpWhere = {'search': data.field}
            gpList()
            return false;
        });

        $("#box_down").click(function () {
            var box_no = $("#box_no").val();
            // var data = {'search': {'box_no': box_no}, 'export': 1, 'task_type': 1, 'serial_no': '{{$info['serial_no']}}'};//查询导出
            var data = gpData.gpWhere
            data.export = 1
            data.serial_no = '{{$info['serial_no']}}'
            $.post("/goodsPacking/getPackList", data, function (res) {
                if (res.code == 200) {
                    layer.msg('导出成功！', {icon: 1});
                    var exportData = [];
                    $.each(res.data, function (k, v) {
                        exportData.push([v.box_no, v.pk_num, v.status_text]);
                    });
                    exportTable(['箱号', '货品数', '状态'], exportData, "调拨装箱列表导出-{{$info['serial_no']}}");
                } else {
                    layer.msg('请输入箱号！', {icon: 2});
                }
            });
            return false;
        });

        function exportTable(title = [], data = [], name = '数据导出', suffix = 'xls') {
            table.exportFile(title, data, suffix, name);
        }

        var dataTableBox = table.render({
            elem: '#dataTableBox'
            , page: true //开启分页
            , skin: 'line'
            , method: 'post'
            , limit: 5
            , where: {'status': 0}
            , cols: saomiaoCols
            , data: saomiaoData
        });

        var uniqueCodeErrorList = table.render({
            elem: '#uniqueCodeErrorList'
            , skin: 'line'
            , page: false //开启分页
            , height:270
            , method: 'post'
            , limit: Number.MAX_VALUE
            , cols: [[{field: 'unique_code',width:150, title: '店内码'}, {
                field: 'error_msg',
                width:300,
                title: '错误原因'
            }]]
            , data: saomiaoData
        });

        var barcodeErrorList = table.render({
            elem: '#barcodeErrorList'
            , page: false //开启分页
            , height:270
            , skin: 'line'
            , method: 'post'
            , limit: Number.MAX_VALUE
            , cols: [[
                {field: 'barcode', title: '条形码',width: 300},
                {field: 'error_msg', title: '错误原因', width: 300}
            ]]
            , data: saomiaoData
        });

        var scanBarcodeCols = table.render({
            elem: '#scanBarcodeList'
            , page: true //开启分页
            , skin: 'line'
            , method: 'post'
            , even: true
            , limit: 5
            , cols: [[{field: 'barcode',width: 200, title: '条码'},{field: 'updated_at', title: '更新时间',hide:true}, { field: 'num', title: '数量' ,width: 200,event:'barcode_num',config:{type:'input'}}]]
            , data: saomiaoData
        }).config.cols;

        var editBarcodeTable = tableEdit.aopObj(scanBarcodeCols);
        /**
         * 注意：
         * 1、 aopTable.on('tool(xxx)',function (obj) {})
         * 2、 table.on('tool(yyy)',function (obj) {})
         * 如果1中的xxx与2中的yyy字符串相同时，不能同时用，用了会造成后调用者覆盖前调用者。
         * 应该直接用1来代替2，因为1中包含了2中的事件。
         * 如果不相同，则可以同时使用。
         **/
        editBarcodeTable.on('tool(scanBarcodeList)',function (obj) {
            var field = obj.field; //单元格字段
            var value = obj.value; //修改后的值
            var data = obj.data; //当前行旧数据
            var event = obj.event; //当前单元格事件属性值
            var update = {};
            update[field] = value;
            console.log("编辑操作",field,value,data,event,update);
            if(obj.event === 'barcode_num'){
                if(scanBarCode.has(data.barcode)){
                    var barObj = scanBarCode.get(data.barcode);
                    barObj.num = parseInt(value);
                    barObj.update_at = Date.now();
                    scanBarCode.set(data.barcode,barObj)
                }else{
                    layer.alert("条码非扫描数据");
                    update[field] = data.num;
                    return false;
                }
            }
            obj.update(update);
        });


        var rfidErrorList = table.render({
            elem: '#rfidErrorList'
            , page: false //开启分页
            , height:270
            , skin: 'line'
            , method: 'post'
            , limit: Number.MAX_VALUE
            , cols: [[{field: 'epc_code',width:150, title: 'RFID'},{field: 'code',width:150, title: '店内码/条形码'}, {
                field: 'error_msg',
                width:200,
                title: '错误原因'
            }]]
            , data: saomiaoData
        });

        var fileErrorList = table.render({
            elem: '#fileErrorList'
            , page: false //开启分页
            , height:270
            , skin: 'line'
            , method: 'post'
            , limit: Number.MAX_VALUE
            , cols: [[{field: 'code',width:200, title: '店内码/条码'}, {
                field: 'error_msg',
                width:400,
                title: '错误原因'
            }]]
            , data: saomiaoData
        });


        var dataTableBoxList = table.render({
            elem: '#dataTableBoxList'
            , url: "/goodsPacking/getPackList?serial_no={{ $info['serial_no'] }}&task_type=1"
            , page: true //开启分页
            , method: 'post'
            , toolbar: '#toolbarBoxDelivery'
            , skin: 'line'
            , cols: [[ //表头
                {type: 'checkbox', fixed: 'left'},
                {type: 'numbers'}
                , {field: 'box_no', title: '箱号'}
                , {field: 'lg_serial_no', title: '交接批次'}
                , {field: 'pk_num', title: '货品数'}
                , {field: 'quality_text', title: '是否质检'}
                , {field: 'status_text', title: '状态'}
                , {title: '操作', width: 300, align: 'center', toolbar: '#boxQuality'}
            ]],
            done: function (res, curr, count) {
                $("#already_out_quality").html(res.already_out_quality);
                $("#no_quality").html(res.no_quality);
                $("#already_num").html(res.already_num);
                console.log(this.where);
            }
        });

        var signBoxList = table.render({
            elem: '#signBoxList'
            , url: "/goodsPacking/signList?serial_no={{ $info['serial_no'] }}"
            , page: true //开启分页
            , where: {goods_code_type:1}
            , method: 'post'
            , toolbar: '#toolbarSignBox'
            , skin: 'line'
            , cols: [[ //表头
                {type: 'numbers'}
                , {field: 'goods_code', title: '编号'}
                , {field: 'code_type_text', title: '编号类型'}
                , {field: 'epc_code', title: 'RFID'}
                , {field: 'num', title: '发货数量'}
                , {field: 'sign_num', title: '签收数量'}
            ]],
            done: function (res, curr, count) {
                $("#send_goods_num").html(res.send_num);
                $("#sign_goods_num").html(res.sign_num);
                console.log(this.where);
            }
        });


        element.on('tab(switchSignType)', function (elem) {
            signType = $(this).attr('sign-type');
            if(signType == 3){
                goodsCodes = [];
            }else{
                globalData.isStarted = false;
                //controlRFID();
            }
            console.log('signType='+signType)
        });

        // 工具栏事件
        table.on('toolbar(signBoxList)', function (obj) {
            var id = obj.config.id;
            var checkStatus = table.checkStatus(id);
            var othis = lay(this);
            console.log(obj);
            switch (obj.event) {
                case 'unSignList':
                    var data = {
                        'export': 1,
                        'task_type': 1,
                        'serial_no': '{{$info['serial_no']}}',
                        'un_sign': 1,
                        // 'is_quality': boxDeliveryQuality
                    };//查询导出
                    $.post("/goodsPacking/signList", data, function (res) {
                        if (res.code == 200) {
                            var exportData = [];
                            $.each(res.data, function (k, v) {
                                exportData.push([v.goods_code, v.code_type_text, v.epc_code, v.num, ""+v.sign_num]);
                            });
                            if (exportData.length <= 0) {
                                layer.msg('无可导出数据！', {icon: 2});
                                return;
                            }
                            exportTable(['编号', '编号类型', 'RFID', '发货数量','签收数量'], exportData, "调拨未签收列表导出-{{$info['serial_no']}}");
                            layer.msg('导出成功！', {icon: 1});
                        } else {
                            layer.msg('数据接口异常！', {icon: 2});
                        }
                    });
                    break;
                case 'signBoxDown':
                    // var boxDeliveryStatus = $("#boxDeliveryStatus").val();
                    // var boxDeliveryQuality = $("#boxDeliveryQuality").val();
                    var data = {
                        'export': 1,
                        'task_type': 1,
                        'serial_no': '{{$info['serial_no']}}',
                        // 'status': boxDeliveryStatus,
                        // 'is_quality': boxDeliveryQuality
                    };//查询导出
                    $.post("/goodsPacking/signList", data, function (res) {
                        if (res.code == 200) {
                            var exportData = [];
                            $.each(res.data, function (k, v) {
                                exportData.push([v.goods_code, v.code_type_text, v.epc_code, v.num, ""+v.sign_num]);
                            });
                            if (exportData.length <= 0) {
                                layer.msg('无可导出数据！', {icon: 2});
                                return;
                            }
                            exportTable(['编号', '编号类型', 'RFID', '发货数量','签收数量'], exportData, "调拨签收列表导出-{{$info['serial_no']}}");
                            layer.msg('导出成功！', {icon: 1});
                        } else {
                            layer.msg('数据接口异常！', {icon: 2});
                        }
                    });
                    break;
                case 'signLogList':
                    alertSignLogList();
                    break;
                case 'syncEpcCode':
                    //询问框

                    layer.confirm('确定要同步门店最新绑定EPC？', {
                        btn: ['确定','取消'] //按钮
                    }, function(){
                        syncEpcCode();
                    });
                    break;
                case 'enterSign':
                    layer.confirm('确认签收吗？', function (index) {
                        layer.close(index);
                        var codeType = signType;
                        var boxNo = '';
                        var errorList;
                        var numPre;
                        if(signType == 4){
                            codeType = $("input[name='code_type']:checked").val();
                        }

                        if(signType == 1 ){
                            boxNo = $("#sign_uni_box").val();
                        }

                        if(signType == 3 ){
                            boxNo = $("#sign_epc_box").val();
                        }

                        if(signType == 4){
                            var formData = new FormData();
                            formData.append('source_no', '{{$info['serial_no']}}');
                            formData.append('code_type', codeType);
                            formData.append('box_no', boxNo);
                            formData.append('file', document.getElementById('file').files[0]);
                            $.ajax({
                                url: '/goodsPacking/fileSign',
                                type: 'post',
                                data: formData,
                                dataType: "json",
                                async: false,//这是重要的一步，防止重复提交的
                                cache: false, // 设置为false，上传文件不需要缓存。
                                contentType: false, // 设置为false,因为是构造的FormData对象,所以这里设置为false
                                processData: false,// 设置为false,因为data值是FormData对象，不需要对数据做处理
                                success: function (res) {
                                    if (res.code == 200) {
                                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                                            //刷新表格
                                            table.reload('fileErrorList', {
                                                data: res.data['error_list']
                                            });
                                            //赋值数量
                                            $("#file_saomiao").html(res.data['total']);
                                            $("#file_yichang").html(res.data['error_list'].length);
                                        });
                                        tableReload('signBoxList')
                                    } else {
                                        layer.msg(res.msg, {icon: 2, time: 1500})
                                    }
                                }
                            });
                        }else {
                            var formData = {};
                            formData.source_no = '{{$info['serial_no']}}';
                            formData.code_type = codeType;
                            formData.box_no = boxNo;
                            if(signType == 3){
                                errorList = 'rfidErrorList';
                                numPre = 'rfid_';
                                formData.code_list = goodsCodes;
                            }else if(signType == 1){
                                errorList = 'uniqueCodeErrorList';
                                numPre = 'uni_';
                                var uniqueCodes = $.trim($("#uniqueCodeInput").val()).split(/[(\r\n)\r\n]+/);
                                uniqueCodes = $.unique(uniqueCodes);
                                formData.code_list = uniqueCodes;
                            }else if(signType == 2){
                                errorList = 'barcodeErrorList';
                                numPre = 'bar_';
                                var tmepList = [];
                                scanBarCode.forEach((v,k)=>{
                                    tmepList.push(v) ;
                                });
                                formData.code_list = tmepList;
                            }

                            $.ajax({
                                url: '/goodsPacking/signCode',
                                type: 'post',
                                data: JSON.stringify(formData),
                                dataType: "json",
                                contentType: 'application/json',
                                cache: false, // 设置为false，上传文件不需要缓存。
                                success: function (res) {
                                    if (res.code == 200) {
                                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                                            //刷新表格
                                            table.reload(errorList, {
                                                page: {
                                                    curr: 1
                                                }
                                                , data: res.data['error_list']
                                            });
                                            //赋值数量
                                            $("#"+numPre+"saomiao").html(res.data['total']);
                                            $("#"+numPre+"yichang").html(res.data['error_list'].length);
                                        });
                                        tableReload('signBoxList')
                                    } else {
                                        layer.msg(res.msg, {icon: 2, time: 1500})
                                    }
                                }
                            });
                        }

                    });

                    //layer.msg('完成发货操作');
                    break;
                case 'refresh':
                    tableReload('dataTableBoxList');
                    break;
            }
        });

        var dataTableBoxDeliveryList = table.render({
            elem: '#dataTableBoxDeliveryList'
            , url: "/backOrder/getBoxDeliveryList?serial_no={{ $info['serial_no'] }}&task_type=1"
            , page: true //开启分页
            , skin: 'line'
            , method: 'post'
            , cols: [[ //表头
                {type: 'checkbox', fixed: 'left'}
                , {type: 'numbers', title: '序号'}
                , {field: 'box_no', title: '箱号', width: 170}
                , {field: 'serial_no', title: '交接批次', width: 170}
                , {field: 'send_type_text', title: '承运方式'}
                , {field: 'express_name', title: '国内承运人'}
                , {field: 'express_code', title: '承运单号'}
                , {field: 'freight_price', title: '承运运费'}
                , {field: 'send_box', title: '箱数'}
                , {field: 'shoes_num', title: '鞋类双数'}
                , {field: 'pk_num', title: '货品数'}
                , {field: 'express_time', title: '发货时间', width: 150}
                , {field: 'transit_day', title: '在途时效(天)'}
                , {field: 'car_code', title: '车牌照'}
                , {field: 'linkman', title: '司机'}
                , {field: 'mobile', title: '联系方式'}
                , {field: 'remark', title: '备注'}
                , {field: 'admin_name', title: '操作人'}
                , {field: 'created_at', title: '操作时间', width: 150}
                , {fixed: 'right', title: '操作', width: 180, align: 'center', toolbar: '#boxDeliveryTool'}
            ]]
            , defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            ,done:function(res, curr, count){
                let table_data = res.data
                let trNum = count;
                if (res.data){
                    for(let i = 0;i<res.data.length;i++){
                        let state = res.data[i].status; //根据status状态判断，不为0时，禁止勾选
                        if(state != 3){
                            var index = res.data[i]['LAY_TABLE_INDEX'];
                            $("div[lay-id='dataTableBoxDeliveryList'] .layui-table tr[data-index="+index+"] input[type='checkbox']").prop('disabled',true);
                            $("div[lay-id='dataTableBoxDeliveryList'] .layui-table tr[data-index="+index+"] input[type='checkbox']").next().addClass('layui-btn-disabled');
                            $("div[lay-id='dataTableBoxDeliveryList'] .layui-table tr[data-index=' + index + '] input[type='checkbox']").prop('name', 'eee');
                        }
                    }
                }
            }
        });
        $('#box_delivery_void').on('click', function (data) {
            var checkStatus = table.checkStatus('dataTableBoxDeliveryList');
            var boxIds = [];
            var data = checkStatus.data;
            console.log(data);
            $.each(data, function (k, v) {
                boxIds.push(v['box_id']);
            })
            if (boxIds.length == 0) {
                layer.msg('请选择箱号发货记录', {icon: 2});
                return false;
            }
            var boxStr = boxIds.join(',');
            console.log(boxStr);
            boxDeliveryVoid(boxStr);
            return false;
        });

        table.on('tool(dataTableBoxDeliveryList)', function (obj) {
            var data = obj.data;
            console.log(data);
            if (obj.event === 'box_delivery_cancel') {
                layer.confirm('确定要作废发货记录吗？', {
                    btn: ['确定', '取消'] //按钮
                }, function () {
                    boxDeliveryVoid(data['box_id']);
                }, function () {
                    console.log('取消');
                });
            }
            if (obj.event === 'box_view_img') {
                layer.open({
                    title: '查看交接图片',
                    type: 2,
                    area: ['70%', '80%'],
                    content: "/logisticsInfo/viewImg?id="+data['lg_id']
                });
                return false;
            }
        })

        /**
         *
         * @param boxIds
         */
        function boxDeliveryVoid(boxIds = '') {
            $.ajax({
                url: "/goodsPacking/cancelBox?box_id=" + boxIds,
                type: 'post',
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    if (data.code == 200) {
                        layer.msg(data.msg, {
                            icon: 1,
                            time: 2000
                        }, function () {
                            tableReload('dataTableBoxDeliveryList');
                        });
                    } else {
                        layer.msg(data.msg, {icon: 2});
                    }
                },
                error: function (e) {
                    layer.msg('提交失败', {icon: 2});
                },
            });
        }

        /**
         *
         * @param id
         * @param where
         * @param data
         * @returns {boolean}
         */
        function tableReload(id, where = {}, cols = [], data = []) {
            console.log('tableReload，id='+id);
            console.log('tableReload-where=',where);
            table.reload(id, {
                where: where
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
                , cols: cols
                , data: data
                ,done: function(res, curr, count){
                    console.log('清空上次搜索的参数')
                    // table reload 有个坑：reload时where参数会保留上次的参数，此处每次请求完，清空where
                    // this.where={};
                }
            });
            return false;
        }

        //触发单元格工具事件
        table.on('tool(dataTableBoxList)', function (obj) { // 双击 toolDouble
            var data = obj.data;
            console.log(data);
            if (obj.event === 'deliveryExpress') {
                boxDelivery(data['id']);
            }
        });

        /**
         * @param boxIdStr
         */
        function boxDelivery(boxIdStr) {
            layer.open({
                title: '物流发货',
                type: 2,
                area: ['70%', '80%'],
                content: "/backOrder/express?id={{$info['id']}}&task_type=1&box_id=" + boxIdStr,
                end: function () {
                    //刷新表单
                    tableReload('dataTableBoxList');
                }
            });
        }

        /**
         * @param boxIdStr
         */
        function alertSignLogList() {
            iframeTools.addTab('签收记录', '/goodsPacking/signLogList?serial_no={{$info['serial_no']}}');
        }

        // 工具栏事件
        table.on('toolbar(dataTableBoxList)', function (obj) {
            var id = obj.config.id;
            var checkStatus = table.checkStatus(id);
            var othis = lay(this);
            console.log(obj);
            switch (obj.event) {
                case 'boxDeliveryDown':
                    var boxDeliveryStatus = $("#boxDeliveryStatus").val();
                    var boxDeliveryQuality = $("#boxDeliveryQuality").val();
                    var data = {
                        'export': 1,
                        'task_type': 1,
                        'serial_no': '{{$info['serial_no']}}',
                        'status': boxDeliveryStatus,
                        'is_quality': boxDeliveryQuality
                    };//查询导出
                    $.post("/goodsPacking/getPackList", data, function (res) {
                        console.log(res);
                        if (res.code == 200) {
                            var exportData = [];
                            $.each(res.data, function (k, v) {
                                exportData.push([v.box_no, v.pk_num, v.quality_text, v.status_text]);
                            });
                            if (exportData.length <= 0) {
                                layer.msg('无可导出数据！', {icon: 2});
                                return;
                            }
                            exportTable(['箱号', '货品数', '是否质检', '发货状态'], exportData, "调拨装箱质检列表导出-{{$info['serial_no']}}");
                            layer.msg('导出成功！', {icon: 1});
                        } else {
                            layer.msg('数据接口异常！', {icon: 2});
                        }
                    });
                    break;
                case 'expressDelivery':
                    var boxIds = [];
                    var data = checkStatus.data;
                    $.each(data, function (k, v) {
                        if (v['status'] == 1) {
                            boxIds.push(v['id']);
                        }
                    })
                    if (boxIds.length == 0) {
                        layer.msg('请选择有效的待发货记录', {icon: 2});
                        break;
                    }
                    var boxStr = boxIds.join(',');
                    boxDelivery(boxStr);
                    break;
                case 'finishDelivery':
                    layer.confirm('确认完成发货吗？', function (index) {
                        layer.close(index);
                        var load = layer.load();
                        var data = {'id': '{{$info['id']}}'};//查询导出
                        $.post("/allot/finishSend", data, function (res) {
                            layer.close(load);
                            if (res.code == 200) {
                                layer.msg('发货成功！', {icon: 1});
                                tableReload('dataTableBoxList');
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        });
                    });

                    //layer.msg('完成发货操作');
                    break;
                case 'refresh':
                    tableReload('dataTableBoxList');
                    break;
                case 'search':
                    var boxDeliveryStatus = $("#boxDeliveryStatus").val();
                    var boxDeliveryQuality = $("#boxDeliveryQuality").val();
                    tableReload('dataTableBoxList', {'status': boxDeliveryStatus, 'is_quality': boxDeliveryQuality});
                    $("#boxDeliveryStatus").val(boxDeliveryStatus);
                    $("#boxDeliveryQuality").val(boxDeliveryQuality);
                    break;
            }
        });

        //箱唛打印
        $("#box_print").click(function () {
            //获取配置数据
            var checkStatus = table.checkStatus('dataTable2');
            var data = checkStatus.data;

            if (data.length <= 0) {
                layer.msg('请选择要打印的箱号！', {icon: 2});
                return;
            }

            let title = getAllotTitle(gpData.allot_remark)
            data.map(item => {
                item.title = title
                item.allot_id = allot_id
                item.weight = item.weight && item.weight != 0 ? item.weight : 0
            })
            allotBoxPrint(data,true);
            //遍历打印
            // $.each(data, function (k, v) {
            //     LODOP.PRINT_INITA("0", "0", "80mm", "40mm", "");
            //     LODOP.SET_PRINT_PAGESIZE(1, '80mm', '40mm', "");
            //     LODOP.SET_PRINT_STYLE("FontSize", 11);
            //     LODOP.SET_PRINT_STYLE("FontName", "微软雅黑");
            //     LODOP.SET_PRINT_COPIES(1);
            //
            //     LODOP.SET_PRINT_STYLE("Bold", 1);
            //     LODOP.ADD_PRINT_TEXT("2mm", 0, "80mm", "5mm", v.tips);
            //     LODOP.SET_PRINT_STYLEA(0,"Alignment",2);
            //     LODOP.SET_PRINT_STYLE("FontSize", 10);
            //     LODOP.SET_PRINT_STYLE("FontName", "微软雅黑");
            //     LODOP.SET_PRINT_STYLE("Bold", 0);
            //
            //     LODOP.ADD_PRINT_BARCODE("8mm", "10mm", "60mm", "25mm", "128Auto", v.box_no);
            //
            //     LODOP.ADD_PRINT_TEXT("9mm", "4mm", "25", "30mm", "货品共" + v.pk_num + "件");
            //     LODOP.SET_PRINT_STYLE("FontSize", 10);
            //     LODOP.SET_PRINT_STYLE("FontName", "微软雅黑");
            //
            //     //top left w h
            //     LODOP.ADD_PRINT_TEXT("9mm", "70mm", "29", "30mm", "包装重" + v.weight + "kg");
            //     LODOP.SET_PRINT_STYLE("FontSize", 10);
            //     LODOP.SET_PRINT_STYLE("FontName", "微软雅黑");
            //
            //     //LODOP.PREVIEW();
            //     LODOP.PRINT();
            // })
        });

        function getAllotTitle(title) {
            // 如果调拨备注里有“批量生成”四个字，则提取这四个字之前的文字作为标题，否则标题默认为空
            let pos = title.indexOf('批量生成')
            if (pos > -1) {
                title = title.substring(0, pos);
            } else {
                title = ''
            }
            return title
        }

        // 重置按钮不会重置图片组，则需单独清空
        $('#reset').on('click', function () {
            $('#uploader-list').html('');
        });

        /*******************************发货相关结束***********************/

        function renderData() {
            if(is_exception){
                $("#input_box").addClass('clr_red');
            }else{
                $("#input_box").removeClass('clr_red');
            }
            var normalBoxNum = 0;
            var normalGoodsNum = 0;
            var exceptionBoxText = '';
            var normalBoxText = '';
            var exceptionBoxNum = Object.keys(exceptionBox).length;
            console.log('normalBox',normalBox);
            for (var code in normalBox){
                normalBoxNum ++ ;
                normalBoxText += code +'\n';
                normalGoodsNum += parseInt(normalBox[code].goods_num);
            }
            $('#normal_goods_num').text(normalGoodsNum);
            $('#normal_box_num').text(normalBoxNum);
            $('#exception_box_num').text(exceptionBoxNum);
            console.log(exceptionBoxNum);
            for (var k in exceptionBox){
                exceptionBoxText += exceptionBox[k]+'\n';
            }

            $("#exception_input").val(exceptionBoxText);
            $("#multi_boxno").val(normalBoxText);
        }


        @if($info['status'] == 2)
        var goodsCols = [[
                {type:'numbers', width:80, title: 'ID'}
                ,{field:'allot_no', title: '调拨单号'}
                ,{field:'brand_name', title: '品牌'}
                ,{field:'category_name', title: '品类'}
                ,{field:'sku_id',title: 'sku'}
                ,{field:'allot_num', width:80, title: '任务数'}
            ]];
        @else
        var goodsCols = [[
                {type:'numbers', width:80, title: 'ID'}
                ,{field:'allot_no', title: '调拨单号'}
                ,{field:'brand_name', title: '品牌'}
                ,{field:'category_name', title: '品类'}
                ,{field:'sku_id',title: 'sku'}
                ,{field:'unique_code',title: '店内码'}
                ,{field:'barcode',title: '条形码'}
                ,{field:'num', width:80, title: '任务数'}
                ,{field:'out_num', width:80, title: '出库数'}
                ,{field:'send_num', width:80, title: '发货数'}
                ,{field:'sign_num', width:80, title: '签收数'}
                ,{field:'receive_num', width:80, title: '到货数'}
                ,{field:'pack_num', width:80, title: '装箱数'}
            ]];
        @endif

        $('#logistics').on('click', function(){
            iframeTools.layer_iframe('到货登记', '/LogisticsInfo/index?id='+arrival_id+'&rela_type=3');
        });

        table.render({
            elem: '#goods_list'
            ,url:'/allot/detail_list?id={{$info["id"]}}'
            ,cols: goodsCols
            ,page: true
        });

        table.render({
            elem: '#box_list'
            ,url:'/goodsPacking/taskList?source_no={{$info["serial_no"]}}'
            ,cols: [[
                {type:'numbers',  title: 'ID'}
                ,{field:'box_no', title: '箱号'}
                // ,{field: 'num', title: '数量',templet: function (d) {
                //         if (d.bill.length == 0) {
                //             return '<span>'+ 0 +'</span>'
                //         } else {
                //             return '<span>'+d.bill[0]['goods_code_total'] +'</span>'
                //         }
                //     }}
                ,{field:'goods_code_total',title: '数量'}
                ,{field:'status_name',title: '状态'}
                ,{field:'right', title: '查看明细', templet:"#box_detail_panel"}
            ]]
            ,page: true
        });


        table.render({
            elem: '#send_list',
            height: 'full-200'
            ,url:'/LogisticsInfo/list?rela_no={{$info["serial_no"]}}'
            ,cols: [[
                {type:'numbers', width:80, title: 'ID'}
                , {field: 'express', title: '物流公司',templet: function (d) {
                        if (d.express.name) {
                            return '<span>'+ d.express.name +'</span>'
                        } else {
                            return '<span>'+ '' +'</span>'
                        }
                    }}
                ,{field:'express_code', title: '物流单号'}
                ,{field:'send_box',title: '箱数'}
                ,{field:'send_num', title: '货品数'}
                ,{field:'remark', title: '备注'}
                ,{field:'image_list', title: '附件',templet: function (d) {
                        var image_list = JSON.parse(d.image_list)
                        if (image_list) {
                            var content = '';
                            for (var i in image_list){
                                content += '<span><a href="'+image_list[i]+'">附件'+(parseInt(i) + 1)+'</a></span> '
                            }
                            return content
                        } else {
                            return '<span>'+ '' +'</span>'
                        }
                    }}
                ,{field:'admin_name', title: '操作人'}
                ,{field:'express_time', title: '发货时间'}
                ,{field:'right', title: '操作', templet:"#send_detail_panel"}
            ]]
            ,page: true
        });

        table.render({
            elem: '#in_list'
            ,url:'/instore/list?order_no={{$info["serial_no"]}}&warehouse_ids={{$info["in_w_id"]}}'
            ,cols: [[
                {type:'numbers',  title: 'ID'}
                ,{field:'final_num',title: '货品数'}
                ,{field:'status_name', title: '状态'}
                ,{field:'created_at', title: '创建时间'}
                ,{field:'finish_at', title: '完成时间'}
                ,{field:'admin_name', title: '创建人'}
                ,{field:'right', align:'center',width:400, title: '操作', toolbar:"#in_detail_panel"}
            ]]
            ,page: true
        });
        //查询
        form.on('submit(query_wait)', function (data) {
            table.reload('goods_list', {where: data.field})
            return false;
        });

        //查询
        form.on('submit(query_done)', function (data) {
            table.reload('done_list', {where: data.field})
            return false;
        });

        //查询
        form.on('submit(query_exception)', function (data) {
            table.reload('exception_list', {where: data.field})
            return false;
        });

        //导出
        form.on('submit(export)', function (data) {
            $.post('/allot/export_detail_list', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.open(r.data.url);
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });

        //监听工具条
        table.on('tool(in_list)', function (obj) { //注：tool是工具条事件名，dataTable是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            if (layEvent === 'viewIn') {
                iframeTools.addTab('入库单明细', '/instore/detail?id=' + data.id);
            }else if (layEvent === 'cancelIn') {
                $.post('/instore/cancel?id='+data.id, {}, function (r) {
                    var loading = $(this).find('.layui-icon');
                    if (loading.is(":visible")) return;
                    loading.show();
                    if (r.code === 200) {
                        layer.alert(r.msg, function(index){
                            layer.close(index);
                            location.reload();
                        });
                    } else {
                        layer.alert(r.msg);
                    }
                    loading.hide();
                });
            }else if (layEvent === 'finishIn') {
                $.post('/instore/finish?id='+data.id, {}, function (r) {
                    var loading = $(this).find('.layui-icon');
                    if (loading.is(":visible")) return;
                    loading.show();
                    if (r.code === 200) {
                        layer.alert(r.msg, function(index){
                            layer.close(index);
                            location.reload();
                        });
                    } else {
                        layer.alert(r.msg);
                    }
                    loading.hide();
                });
            }
        });

        //监听工具条
        table.on('tool(box_list)', function (obj) { //注：tool是工具条事件名，dataTable是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            console.log(layEvent);
            if (layEvent === 'view') {
                iframeTools.addTab('装箱明细', '/goodsPacking/detail?id=' + data.id);
            }else if (layEvent === 'cancel') {
                layer.confirm('确认作废吗？', function (index) {
                    layer.close(index);
                    var load = layer.load();
                    $.post("/goodsPacking/cancel", {id: data.id}, function (res) {
                        layer.close(load);
                        if (res.code == 200) {
                            window.location.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 1500})
                        }
                    });
                });
            }
        });

        //监听工具条
        table.on('tool(send_list)', function (obj) { //注：tool是工具条事件名，dataTable是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            console.log(layEvent);
            if (layEvent === 'view') {
                iframeTools.addTab('货品装箱', '/goodsPacking/list?task_type=1&source_no=' + data.rela_no);
            }
        });

        $('.in_btn').click(function () {
            $.post('/instore/add?type=3&batch_type=3&serial_no='+$(this).attr('data-serial_no'), {}, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    iframeTools.addTab('货品入库', '/instore/import?id='+r.data.id);
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
        })

        $('.create_btn').click(function () {
            $.post('/outStock/add?type=23&serial_no='+$(this).attr('data-serial_no'), {}, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    location.reload();
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
        })

        $('.cancel_btn').click(function () {
            $.post('/allot/cancel?id='+$(this).attr('data-id'), {}, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    location.reload();
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
        })

        $('.send_btn').click(function () {
            $("#send_panel").show();
            $("#send_log_panel").hide();
        })

        $('.send_log_btn').click(function () {
            $("#send_panel").hide();
            $("#send_log_panel").show();
        })

        $('.in_list_btn').click(function () {
            $("#in_panel").hide();
            $("#in_list_panel").show();
        })

        $('.single_btn').click(function () {
            var box_no = $("#single_boxno").val();
            $.post('/goodsPacking/checkTaskBox?box_no='+box_no, {}, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                $("#input_box").text(box_no);
                if (r.code === 200) {
                    if(r.data){
                        for (var code in r.data){
                            if(r.data[code].source_no == serial_no){
                                normalBox[code] = r.data[code];
                                is_exception = false;
                            }else{
                                is_exception = true;
                                exceptionBox[code] = "箱号"+box_no+"不属于此任务";
                                layer.alert("箱号"+box_no+"不属于此任务");
                            }
                        }
                    }else{
                        exceptionBox[box_no] = "箱号"+box_no+"不存在";
                        layer.alert("箱号"+box_no+"不存在");
                        is_exception = true;
                    }
                    renderData();
                } else {
                    layer.alert(r.msg);
                }

                loading.hide();
            });
        })

        $('.check_btn').click(function () {
            var box_list = $("#multi_boxno").val();
            var params = {};
            params['box_no'] = box_list.split('\n');
            console.log(params);
            $.post('/goodsPacking/checkTaskBox', params, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    normalBox = {};
                    exceptionBox = {};
                    is_check = true;
                    for (var key in params.box_no){
                        var code = params.box_no[key];
                        if(!code){
                            continue;
                        }
                        if( r.data ){
                            if(r.data[code]){
                                if(r.data[code].source_no == serial_no){
                                    normalBox[code] = r.data[code];
                                }else{
                                    exceptionBox[code] = "箱号"+code+"不属于此任务";
                                }
                            }else{
                                exceptionBox[code] = "箱号"+code+"不存在";
                            }
                        }else{
                            exceptionBox[code] = "箱号"+code+"不存在";
                        }
                    }
                    renderData();
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
        })

        $('.finish_btn').click(function () {
            $(this).addClass("layui-btn-disabled").attr("disabled",true);
            $.post('/allot/stat?id='+$(this).attr('data-id'), {}, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    $('#send_count').text(r.data.out_num);
                    $('#in_num').text(r.data.in_num);
                    $('#diff_num').text(r.data.out_num - r.data.in_num);
                    layer.open({
                        type: 1,
                        content: $('#finish_panel') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            }).always(function(){
                $('.finish_btn').removeClass("layui-btn-disabled").attr("disabled",false);
            });
        })

        $('.finish_diff_btn').click(function () {
            $(this).addClass("layui-btn-disabled").attr("disabled",true);
            var params = {};
            params.diff_id = $(this).attr('data-id');
            params.allot_id = {{$info["id"]}};
            $.post('/allot/over_diff', params, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.msg) {
                    layer.alert(r.msg);
                }
                loading.hide();
            }).always(function(){
                $('.finish_diff_btn').removeClass("layui-btn-disabled").attr("disabled",false);
            });
        })

        //导出
        form.on('submit(overAllot)', function (data) {
            $.post('/allot/over', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.location.reload();
                    });
                    console.log(r);
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });
        var handle_types = [
            {name:1,value:"配送漏发"},
            {name:2,value:"配送丢失"},
            {name:3,value:"配送报损"},
            {name:4,value:"签收漏签"}
            ];
        var liabler_types = [
            {name:1,value:"调出方"},
            {name:2,value:"调入方"}
        ];
        var diff_cols = table.render({
            elem: '#diff_list',
            height: 'full-200'
            ,url:'/allot/diff_list?diff_id={{$diffInfo["id"] ?? 0}}'
            ,cols: [[ //标题栏
                {type:'numbers',  title: 'ID'}
                ,{field: 'unique_code', title: '店内码'}
                ,{field: 'barcode', title: '条形码'}
                ,{field: 'sku_id', title: 'SKU'}
                ,{field: 'brand_name', title: '品牌'}
                ,{field: 'category_name', title: '品类'}
                ,{field: 'diff_num', title: '差异数量'}
                ,{field: 'pending_num', title: '待处理数量'}
                ,{field: 'handle_num', title: '处理数量',event:'handle_num',config:{type:'input'}}
                ,{field: 'diff_reason', title: '差异原因',event:'diff_reason',config:{type:'select',data:handle_types},templet:function (d) {
                        return d.diff_reason && d.diff_reason.value ? d.diff_reason.value : '';
                    }}
                ,{field: 'liabler', title: '归属方',event:'liabler',config:{type:'select',data:liabler_types},templet:function (d) {
                        return d.liabler && d.liabler.value ? d.liabler.value : '';
                    }}
                ,{field: 'right', title: '操作',  toolbar: '#diffBtn'}
            ]]
            //,skin: 'line' //表格风格
            ,even: true
            ,page: true //是否显示分页
            //,limits: [5, 7, 10]
            //,limit: 5 //每页默认显示的数量
        }).config.cols;

        var aopTable = tableEdit.aopObj(diff_cols);
        /**
         * 注意：
         * 1、 aopTable.on('tool(xxx)',function (obj) {})
         * 2、 table.on('tool(yyy)',function (obj) {})
         * 如果1中的xxx与2中的yyy字符串相同时，不能同时用，用了会造成后调用者覆盖前调用者。
         * 应该直接用1来代替2，因为1中包含了2中的事件。
         * 如果不相同，则可以同时使用。
         **/
        aopTable.on('tool(diff_list)',function (obj) {
            var field = obj.field; //单元格字段
            var value = obj.value; //修改后的值
            var data = obj.data; //当前行旧数据
            var event = obj.event; //当前单元格事件属性值
            var update = {};
            update[field] = value;
            console.log("编辑操作",field,value,data,event,update);
            //把value更新到行中

            if(obj.event === 'handle_num'){
                if(value > data.pending_num){
                    layer.alert("处理数量不能大于待处理数量");
                    update[field] = 0;
                    return false;
                }
            }
            obj.update(update);
            if(obj.event === 'commit'){
                var params = {};
                params.diff_id = data.diff_id;
                params.handle_num = data.handle_num;
                params.diff_reason = data.diff_reason.name;
                params.liabler = data.liabler.name;
                params.detail_id = data.id;

                $.post('/allot/handle_diff', params, function (r) {
                    var loading = $(this).find('.layui-icon');
                    if (loading.is(":visible")) return;
                    loading.show();
                    if (r.code === 200) {
                        update['pending_num'] = data.pending_num - data.handle_num;
                        update['handle_num'] = 0;
                        obj.update(update);
                        table.reload("handle_list");
                        layer.alert(r.msg);
                    } else {
                        layer.alert(r.msg);
                    }
                    loading.hide();
                });
                return false;
            }

        });


        table.render({
            elem: '#handle_list'
            ,url:'/allot/diff_result?diff_id={{$diffInfo["id"] ?? 0}}'
            ,cols: [[ //标题栏
                {type:'numbers',  title: 'ID'}
                ,{field: 'unique_code', title: '店内码'}
                ,{field: 'barcode', title: '条形码'}
                ,{field: 'sku_id', title: 'SKU'}
                ,{field: 'brand_name', title: '品牌'}
                ,{field: 'category_name', title: '品类'}
                ,{field: 'handle_num', title: '处理数量'}
                ,{field: 'diff_reason_name', title: '差异原因'}
                ,{field: 'liabler_name', title: '归属方'}
                ,{field: 'right', title: '操作', toolbar: '#resultBtn'}
            ]]
            //,skin: 'line' //表格风格
            ,even: true
            ,page: true //是否显示分页
            //,limits: [5, 7, 10]
            //,limit: 5 //每页默认显示的数量
        });
        //头工具栏事件
        table.on('tool(handle_list)', function(obj){
            var data = obj.data; //当前行旧数据
            console.log("删除日志",obj.event,data);
            switch(obj.event){
                case 'cancel':
                    var params = {};
                    params.diff_id = data.diff_id;
                    params.result_id = data.id;

                    $.post('/allot/cancel_result', params, function (r) {
                        var loading = $(this).find('.layui-icon');
                        if (loading.is(":visible")) return;
                        loading.show();
                        if (r.code === 200) {
                            obj.del();
                            table.reload("diff_list");
                            layer.alert(r.msg);
                        } else {
                            layer.alert(r.msg);
                        }
                        loading.hide();
                    });
                    return false;
                    break;
            };
        });

        //查询
        form.on('select(search_type)', function (data) {
            $("#search_value").val('');
        });
        //查询
        form.on('submit(query_diff)', function (data) {
            table.reload('diff_list', {where: data.field})
            return false;
        });
        //导出
        form.on('submit(export_diff)', function (data) {
            $.post('/allot/diff_list?is_export=1', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.open(r.data.url);
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });
        //查询
        form.on('submit(query_result)', function (data) {
            table.reload('handle_list', {where: data.field})
            return false;
        });
        //导出
        form.on('submit(export_result)', function (data) {
            $.post('/allot/diff_result?is_export=1', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.open(r.data.url);
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });

        $("#diff_tab_btn").click();

        var epcUrl = 'http://127.0.0.1:30010';

        var timer;
        var goodsCodes;
        // 全局变量集合
        var globalData = {
            isStarted: false
        }
        function controlRFID () {
            // 根据开关状态控制RFID开关
            if (globalData.isStarted ) { // 开启
                // 先清空
                // goodsCodes = []
                // 开启RFID并持续读取
                connectAndRead()
                $("#rfid_label").text("正在扫描")
            } else { // 关闭
                $("#rfid_label").text("已暂停")
                stopAndDisconnect()
                // 请求校验epc
                checkEpc(goodsCodes)
            }
        }

        $("#rfid_confirm").click(function(){
            trunCollect();
        })

        function trunCollect(){
            globalData.isStarted = !globalData.isStarted
            controlRFID();
        }

        $(document).keyup(function(event){
            if (event.keyCode == 32 && signType == 3) {// 32 Space（空格键）
                trunCollect();
                return false
            }
        });

        function connectAndRead () {
            // 链接并启动
            connectAndStart()
            // 持续读取
            continueRead()
        }
        function stopAndDisconnect() {
            clearInterval(timer);
            curlPost(epcUrl + '/api/Reader/stopAndDisconnect')
        }

        function connectAndStart() {
            curlPost(epcUrl + '/api/Reader/connectAndStart');
        }

        function continueRead() {
            console.log('持续读取')
            timer=setInterval(Read,800);
        }

        function Read() {
            console.log('读取')
            curlPost(epcUrl + '/api/Reader/continueRead');
        }

        function curlPost(url,params) {
            console.log(url);
            $.ajax({
                url: url,
                type: 'POST',
                beforeSend:false,
                data:{StartReadParam:{MemoryBank:'EPC',ExcludeDuplication:'true'}},
                dataType: 'JSON',
                success: function (result) {
                    console.log('epc请求success结果==',result);
                    if ('boolean' != typeof(result)) {
                        // 将返回结果提取，去重
                        var epcRes = comm.array_unique(comm.array_column(result.Data,'Epc'))
                        console.log('goodsCodes11111==',goodsCodes);
                        // 向全局数组写数据
                        goodsCodes.push.apply(goodsCodes,epcRes) // 合并数组
                        console.log('goodsCodes22222==',goodsCodes);
                        console.log('合并后的新数组==',comm.array_unique(goodsCodes))
                        goodsCodes = comm.array_unique(goodsCodes)
                        console.log('finalGoodsCodes==',goodsCodes)
                        // 去重
                        $('#already_rfid_num').text(goodsCodes.length)
                    }
                },
                error: function (error) {
                    console.log(typeof (error));
                    console.log('epc请求结果fail==',error);
                    layer.msg('设备读取EPC码失败',{icon:2,time:1500})
                }
            });
        }


        //////////////////////////////////////////////////////////////////////////////装箱相关-start//////////////////////////////////////////////////////////
        // 装箱相关 - 20221020 - mwp
        init()
        function init() {
            // 下载按钮初始置灰
            exportBtnStatus()
        }

        function exportBtnStatus() {
            if (gpData.exportData.length === 0) {
                $('#exportGpSelectDetail').addClass('layui-btn-disabled')
                $('#batchPrint').addClass('layui-btn-disabled')
                $('#batchPrint').attr("disabled",true);
            } else {
                $('#exportGpSelectDetail').removeClass('layui-btn-disabled')
                $('#batchPrint').removeClass('layui-btn-disabled')
                $('#batchPrint').attr("disabled",false);
            }
        }

        $('#exportGpSelectDetail').on('click',function (obj) {
            let ids = gpData.exportData.map(item => item.id)
            console.log('ids===============',ids)
            if (gpData.exportData.length > 0) {
                console.log('可以下载')
                bulkExportPackingBills(ids)
            } else {
                console.log('不可以下载')
                return false
            }
        })

        table.on('checkbox(dataTable2)', function(obj){ //test 是 table 标签对应的 lay-filter 属性
            console.log(obj); //选中行的相关数据
            console.log(obj.type); //如果触发的是全选，则为：all，如果触发的是单选，则为：one
            let checkStatus = table.checkStatus('tableBar')
            console.log(checkStatus.data)
            let curItem = obj.data
            console.log('curItem=',curItem)
            if (obj.type === 'one') {
                if (obj.checked) {
                    // 写入数组
                    gpData.exportData.push(obj.data)
                    gpData.selectIds.push(curItem.id)
                } else {
                    // 从数组删除: id不相等的是删除后的集合，然后重新赋值给全局变量
                    gpData.exportData = gpData.exportData.filter(item => item.id !== curItem.id)
                    gpData.selectIds = gpData.selectIds.filter(id => id !== curItem.id)
                }
            } else {
                if (obj.checked) {
                    // 先清空
                    gpData.exportData = []
                    // 全选，将当页数据全部赋值给exportData
                    gpData.exportData = gpData.curPageData
                    gpData.exportData.map(item => {
                        gpData.selectIds.push(item.id)
                    })
                } else {
                    // 全不选，exportData置空
                    gpData.exportData = []
                    gpData.selectIds = []
                }
            }

            console.log('selectIds========',gpData.selectIds)
            exportBtnStatus()
        });

        // 批量下载装箱明细
        function bulkExportPackingBills(ids) {
            //loading层
            gpData.loading = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            $.ajax({
                type: 'post',
                url: "/goodsPacking/bulkExportPackingBills",
                data: {
                    ids,
                },
                success: function (res) {
                    console.log('getLatestWeightLog-res====',res)
                    if (res.data.url) {
                        window.open(res.data.url);
                        layer.msg(res.msg);
                    } else {
                        console.log('异常====',res)
                        layer.msg(res.msg);
                    }
                    layer.close(gpData.loading)
                }
            })
            return false;
        }

        $.ajax({
            type: 'post',
            url: "/goodsPacking/getPackedGoodsTotalBySerialNo",
            data: {
                task_type:1,
                source_no: gpData.source_no
            },
            success: function (res) {
                console.log('goods_total-res====',res)
                if (res.data) {
                    $('#goods_total').text(res.data.total)
                } else {
                    console.log('异常====',res)
                    layer.msg(res.msg);
                }
            }
        })

        $('#batchPrint').click(function () {
            iframeTools.editTab('批量打印装箱清单','/goodsPacking/batchPrintBillList?ids='+gpData.selectIds.join(','))
        })

        $('#batchWeight').click(function () {
            console.log('点击批量称重')
            // 每次打开清空表单
            clearBatchWeightForm()

            layer.open({
                type: 1
                ,title: '批量称重绑铅封'
                ,closeBtn: 1
                ,area: '460px'
                ,shade: 0.1
                ,id: 'LAY_layuipro' //设定一个id，防止重复弹出
                ,resize: true
                ,btn: ['确定','关闭']
                ,btnAlign: 'c'
                ,moveType: 1 //拖拽模式，0或者1
                ,content: $("#batchWeightForm")
                ,yes: function(index, layero){
                    console.log('点击了确认')
                    saveWeightOrLeadSealNo()
                }
                ,btn2: function (index, layero) {
                    console.log('点击了关闭')
                    layer.close(index); //如果设定了yes回调，需进行手工关闭
                }
            });
            // 包裹号默认获取焦点
            getFocus("#batch_box_no")
        })





        function clearBatchWeightForm() {
            $('#batch_box_no').val("")
            $('#batch_lead_seal_no').val("")
            $('#batch_weight').val("")
        }

        // tab切换至装箱，扫描箱号要默认获取焦点
        element.on('tab(tab-all)', function(data){
            if (1 == data.index) {
                console.log('tab切换至装箱列表-获取焦点',data.index); //得到当前Tab的所在下标
                getFocus("#box_btn")
            }
        });

        function getFocus(position) {
            $(position).focus()
        }
        function clearVal(position) {
            $(position).val('')
        }
        function setVal(position,val) {
            $(position).val(val)
        }
        function getVal(position) {
            return $(position).val()
        }

        // 扫描箱号回车
        $("#box_btn").bind('keyup', function(event) {
            var no = $("#box_btn").val();
            console.log('扫描箱号回车keyup，val='+no+', event.keyCode='+event.keyCode)
            // 扫描枪扫描完以后，会有个按下enter键的事件触发，因此监听keyCode=13事件即可，触发校验箱号逻辑
            if (event.keyCode == "13") { // 点击了回车
                // 字符串为空，直接打开装箱页面
                if (no.length === 0) {
                    openAddPage(serial_no,no)
                } else {
                    // 扫描有值，校验通过再打开
                    var res = checkBoxNoOrLeadSealNo(no)
                    console.log('校验结果=',res)
                    if (res.code != 200) {
                        layer.alert(res.msg, {icon: 2},function (index) {
                            // 清空已扫描内容
                            clearVal("#box_btn")
                            // 获取焦点
                            getFocus("#box_btn")
                            layer.close(index)
                        })
                        return
                    }
                    openAddPage(serial_no,no)
                }

            }
        })

        function openAddPage(sourceNo,no) {
            // 校验通过，打开装箱页面
            iframeTools.addTabWithCB('货品装箱', "/goodsPacking/add?flag=add&task_type=1&source_no="+sourceNo+"&no="+no,function (index,url) {
                console.log(`点击了iframeTools的关闭"X"按钮index=${index},url=${url}`)
                realStopAndDisconnect()
            });
        }

        // 校验扫描的箱号或铅封号
        function checkBoxNoOrLeadSealNo(no) {
            // 开启加载
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            var result = false;
            $.ajax({
                type: 'post',
                url: "/goodsPacking/checkBoxNoOrLeadSealNo",
                async: false,
                data: {
                    no
                },
                success: function (res) {
                    layer.close(index)
                    result = res
                    console.log('校验扫描的箱号或铅封号res=',res)
                }
            })
            return result;
        }

        // 校验扫描的箱号是否在指定来源单号下
        function checkBoxNoInSourceNo(source_no,no) {
            // 开启加载
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            var result = false;
            $.ajax({
                type: 'post',
                url: "/goodsPacking/checkBoxNoInSourceNo",
                async: false,
                data: {
                    source_no,
                    no,
                },
                success: function (res) {
                    result = res
                    layer.close(index)
                    console.log('校验扫描的箱号是否在指定来源单号下res1=',res)
                }
            })
            return result;
        }

        // 校验扫描的箱号是否系统系统生成
        function checkIfBoxNoLegal(box_no) {
            // 开启加载
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            var result = false;
            $.ajax({
                type: 'post',
                url: "/goodsPacking/checkIfBoxNoLegal",
                async: false,
                data: {
                    box_no
                },
                success: function (res) {
                    result = res
                    layer.close(index)
                    console.log('校验扫描的箱号是否系统系统生成res1=',res)
                }
            })
            return result;
        }

        // 获取铅封号的绑定信息
        function getLeadSealNosBindInfo(lead_seal_no) {
            // 开启加载
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            var result = false;
            $.ajax({
                type: 'post',
                url: "/goodsPacking/getLeadSealNosBindInfo",
                async: false,
                data: {
                    lead_seal_no
                },
                success: function (res) {
                    layer.close(index)
                    result = res
                    console.log('获取铅封号的绑定信息res1=',res)
                }
            })
            return result;
        }

        // 箱号回车
        $("#batch_box_no").bind('keyup', function(event) {
            var box_no = $("#batch_box_no").val();
            console.log('扫描了箱号，val='+box_no+', keyCode='+event.keyCode)
            // 删除箱号同时清空铅封号输入框
            if (box_no.length == 0) {
                clearVal("#batch_lead_seal_no")
            }
            if (event.keyCode == "13") { // 点击了回车
                console.log('箱号回车，val=',box_no)
                // event.target.blur(); // 将事件委托给失去焦点
                if (box_no.length == 0) {
                    layer.alert('请输入包裹号', {icon: 2},function (index) {
                        // 焦点依然在当前文本框
                        getFocus("#batch_box_no")
                        layer.close(index)
                    })
                    return
                }
                // 校验箱号
                var res = checkBoxNoInSourceNo(serial_no,box_no)
                if (res.code != 200) {
                    layer.alert(res.msg, {icon: 2},function (index) {
                        // 清空已扫描内容
                        clearVal("#batch_box_no")
                        // 获取焦点
                        getFocus("#batch_box_no")
                        layer.close(index)
                    })
                    return
                }
                console.log('校验扫描的箱号是否在指定来源单号下res2=',res)
                var lead_seal_no = res.data?.exists[0]?.lead_seal_no
                console.log('lead_seal_no=',lead_seal_no)
                // 如果箱号关联了铅封号，箱号联动铅封号，并焦点转移“包裹重量(公斤)”文本框
                if (typeof lead_seal_no !== 'undefined' && lead_seal_no.length > 0) {
                    // 关联了铅封号，自动赋值铅封号输入框
                    setVal("#batch_lead_seal_no",lead_seal_no)
                    // 焦点转移“包裹重量(公斤)”文本框
                    getFocus("#batch_weight")
                    return
                }
                // 如果未关联铅封号，系统需要判断当前包裹号是否系统自动生成
                // 校验箱号是否自动生成
                var res = checkIfBoxNoLegal(box_no)
                if (res.code != 200) {
                    layer.alert(res.msg, {icon: 2},function (index) {
                        layer.close(index)
                    })
                    return
                }
                console.log('校验扫描的箱号是否系统系统生成res2=',res)
                // 如果包裹号是自动生成，焦点转移到扫描“铅封号文本框”
                if (res.data.result == 1) {
                    // 铅封号输入框自动获取焦点
                    getFocus("#batch_lead_seal_no")
                    return
                }
                // 如果非自动生成，铅封号默认等于包裹号，且焦点转移“包裹重量(公斤)”文本框；
                // 铅封号输入框默认赋值箱号
                setVal("#batch_lead_seal_no",box_no)
                // 焦点转移“包裹重量(公斤)”文本框
                getFocus("#batch_weight")
            }
        })
            // .bind('blur', function(event) {
            //     var box_no = $("#batch_box_no").val();
            //     console.log('箱号失焦，val=',box_no)
            //     // 校验箱号
            //     checkBoxNo(serial_no,box_no)
            // })

        // 铅封号回车
        $("#batch_lead_seal_no").bind('keyup', function(event) {
            var lead_seal_no = $("#batch_lead_seal_no").val();
            if (event.keyCode == "13") { // 点击了回车
                console.log('铅封号回车，val=',lead_seal_no)
                // event.target.blur(); // 将事件委托给失去焦点
                if (lead_seal_no.length == 0) {
                    // 铅封号为空直接回车，焦点转入包裹称重文本框
                    getFocus("#batch_weight")
                    return
                }
                // 校验铅封号
                var res = getLeadSealNosBindInfo(lead_seal_no)
                if (res.code != 200) {
                    layer.alert(res.msg, {icon: 2},function (index) {
                        // 清空输入框
                        clearVal("#batch_lead_seal_no")
                        // 焦点依然在扫描铅封号文本框
                        getFocus("#batch_lead_seal_no")
                        layer.close(index)
                    })
                    return
                }
                // 铅封号验证通过，焦点转入包裹称重文本框
                getFocus("#batch_weight")

            }
        })
            // .bind('blur', function(event) {
            //     var inputData = $("#batch_lead_seal_no").val();
            //     console.log('铅封号失焦，val=',inputData)
            // })

        // 称重回车
        $("#batch_weight").bind('keyup', function(event) {
            if (event.keyCode == "13") { // 点击了回车
                saveWeightOrLeadSealNo()
            }
        })

        function saveWeightOrLeadSealNo() {
            var weight = $("#batch_weight").val();
            console.log('称重回车，val=',weight)
            // event.target.blur(); // 将事件委托给失去焦点
            // if (weight.length == 0) {
            //     layer.alert('请输入重量', {icon: 2},function (index) {
            //         // 焦点依然在当前文本框
            //         getFocus("#batch_weight")
            //         layer.close(index)
            //     })
            //     return
            // }
            // 提交数据
            var box_no = getVal("#batch_box_no")
            var lead_seal_no = getVal("#batch_lead_seal_no")
            if (box_no.length == 0) {
                layer.alert('请输入包裹号', {icon: 2},function (index) {
                    // 焦点依然在当前文本框
                    getFocus("#batch_box_no")
                    layer.close(index)
                })
                return
            }
            // 保存称重与绑铅封信息
            var res = submitWeightAndLeadSealNo(box_no,lead_seal_no,serial_no,weight)
            if (res.code != 200) {
                layer.alert(res.msg, {icon: 2},function (index) {
                    // 焦点依然在当前文本框
                    getFocus("#batch_weight")
                    layer.close(index)
                })
                return
            }
            layer.msg('操作成功', {icon: 1})
            clearVal("#batch_box_no")
            clearVal("#batch_lead_seal_no")
            clearVal("#batch_weight")
            getFocus("#batch_box_no")
            table.reload('dataTable2')
        }

        // 保存称重与绑铅封信息
        function submitWeightAndLeadSealNo(box_no,lead_seal_no,source_no,weight) {
            console.log(`提交称重绑铅封，weight=${weight}, lead_seal_no=${lead_seal_no}, weight=${weight}`)
            // 开启加载
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            var result = false;
            $.ajax({
                type: 'post',
                url: "/goodsPacking/saveWeightAndLeadSealNo",
                async: false,
                data: {
                    box_no,
                    lead_seal_no,
                    source_no,
                    weight
                },
                success: function (res) {
                    layer.close(index)
                    result = res
                    console.log('保存称重与绑铅封信息res1=',res)
                }
            })
            return result;
        }
            // .bind('blur', function(event) {
            //     var inputData = $("#batch_weight").val();
            //     console.log('称重失焦，val=',inputData)
            // })

        form.on('checkbox(unprinted)', function(data){
            let params = null
            if (data.elem.checked) {
                console.log('未打印-开',data.elem.checked);
                params = {'search': {print_times: 0}}
            }
            // tableReload('dataTable2', params);
            gpData.gpWhere = params
            gpList()
        });

        //////////////////////////////////////////////////////////////////////////////装箱相关-end//////////////////////////////////////////////////////////

        // 同步门店EPC
        function syncEpcCode() {
            //加载层
            var index = layer.load(0, {
                shade: false
            }); //0代表加载的风格，支持0-2
            $.ajax({
                type: 'post',
                url: "/goodsPacking/syncEpcCodeForUniqueCodeBox",
                data: {
                    task_type:1,
                    source_no: gpData.source_no
                },
                success: function (res) {
                    console.log('goods_total-res====',res)
                    layer.close(index)
                    layer.msg(res.msg);
                    if (res.code == 200) {
                        signBoxList.reload()
                    } else {
                        console.log('异常====', res)
                        layer.msg(res.msg);
                        // layer.msg(res.msg,{icon: 5});
                    }
                }
            })
        }

        getPkAdminList()
        function getPkAdminList() {
            var index = layer.load(0, {
                shade: false
            });
            $.ajax({
                type: 'post',
                url: "/goodsPacking/getPkAdminList",
                data: {
                    source_no: gpData.source_no
                },
                success: function (res) {
                    layer.close(index)
                    if (res.code == 200) {
                        console.log('getPkAdminList=',res.data)
                        renderCommSelect('装箱人','pk_admin_id',res.data)
                    } else {
                        console.log('异常====', res)
                        layer.msg(res.msg);
                        // layer.msg(res.msg,{icon: 5});
                    }
                }
            })
        }

        function renderCommSelect (title,selector, data) {
            var option = '<option value="">请选择'+title+'</option>'
            $.each(data,function (index,item) {
                option += "<option value="+item.id+">"+item.name+"</option>"
            })

            var selectObj = $("#"+selector)
            selectObj.append(option)

            form.render("select")
        }

        // 漏装箱记录
        $('#notBoxList').click(function (){
            // 使用漏装箱组件
            showNotBoxListModal(serial_no, null, allot_id);
        })

    })

    // 禁止按空格键时使用页面page down页面下翻滚动事件
    // Prevent Spacebar Doing Page Down
    function PSDPD_KeyCheck(key){
        // Don't modify text editing
        if (key.target.nodeName == "INPUT" || key.target.nodeName == "TEXTAREA" || key.target.nodeName == "SELECT") return;
        if (key.target.hasAttribute("contenteditable") && key.target.getAttribute("contenteditable") == "true") return;
        // Don't modify certain combinations
        if (key.ctrlKey || key.altKey || key.metaKey) return;
        // If it's a space character, kill the event
        if (key.key == ' '){
            key.stopPropagation();
            key.preventDefault();
            return false;
        }
    }
    // Monitor the keydown event
    document.addEventListener('keydown', PSDPD_KeyCheck);

    $(function () {
        //下载货品明细
        $('#down_arrival').click(function () {
            var arrival_id = $('#arrival_id').val();
            var $formArrival = $("#downArrivalProduct");
            $formArrival.append($('<input name = "arrival_id" value = "' + arrival_id + '" />'));
            $formArrival.submit();
        })
    })

</script>
</body>
<div class="layui-card" style="display: none" id="finish_panel">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <input type="hidden" name="id" value="{{$info['id']}}">
            <div class="layui-form-item">
                <div class="layui-row">
                    <label class="layui-form-label" >发货数量:</label>
                    <div  style="line-height: 36px;">
                        <span id="send_count">0</span>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <label class="layui-form-label" >入库数量:</label>
                    <div style="line-height: 36px;">
                        <span id="in_num">0</span>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row clr_red">
                    <label class="layui-form-label" >差异数量:</label>
                    <div style="line-height: 36px;">
                        <span id="diff_num">0</span>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row align-center clr_red">
                    入库数量与发货数量存在差异，
                    <br>
                    确认完成后将无法进行修改。
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="overAllot">确认完成</button>
                </div>
            </div>
        </form>
    </div>
</div>

{{-- 引入漏装箱组件 --}}
@include('components.notBoxList')

</html>
